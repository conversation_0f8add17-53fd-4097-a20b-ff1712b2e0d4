#!/usr/bin/env node
/**
 * Test script to verify sync status monitoring functionality
 * This script tests the real-time event forwarding and sync status updates
 */
declare class SyncStatusMonitoringTest {
    private databaseService;
    private mcpService;
    initialize(): Promise<void>;
    testEventEmission(): Promise<void>;
    testStatusMonitoring(): Promise<void>;
    cleanup(): Promise<void>;
    run(): Promise<void>;
}
export { SyncStatusMonitoringTest };
