#!/usr/bin/env node
"use strict";
/**
 * Test script to verify sync status monitoring functionality
 * This script tests the real-time event forwarding and sync status updates
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncStatusMonitoringTest = void 0;
const sync_manager_service_1 = require("./services/sync-manager.service");
const connection_1 = require("./database/connection");
const service_1 = require("./mcp/service");
const electron_log_1 = __importDefault(require("electron-log"));
// Configure logging for testing
electron_log_1.default.transports.console.level = 'debug';
electron_log_1.default.transports.file.level = 'debug';
class SyncStatusMonitoringTest {
    databaseService = null;
    mcpService = null;
    async initialize() {
        try {
            console.log('🔧 Initializing test environment...');
            // Initialize database
            this.databaseService = connection_1.DatabaseConnection.getInstance();
            await this.databaseService.initialize();
            console.log('✅ Database initialized');
            // Initialize MCP service
            this.mcpService = service_1.MCPService.getInstance();
            await this.mcpService.initialize();
            console.log('✅ MCP service initialized');
            // Initialize sync manager
            if (this.mcpService.isConnected()) {
                await sync_manager_service_1.syncManager.initialize();
                console.log('✅ Sync manager initialized');
            }
            else {
                console.log('⚠️  MCP service not connected, using mock mode');
                await sync_manager_service_1.syncManager.initialize();
            }
        }
        catch (error) {
            console.error('❌ Failed to initialize test environment:', error);
            throw error;
        }
    }
    async testEventEmission() {
        console.log('\n🧪 Testing sync event emission...');
        // Set up event listeners to verify events are emitted
        const events = [];
        sync_manager_service_1.syncManager.on('statusChanged', (status) => {
            events.push({ type: 'statusChanged', data: status });
            console.log('📡 Received statusChanged event:', status);
        });
        sync_manager_service_1.syncManager.on('syncStarted', () => {
            events.push({ type: 'syncStarted' });
            console.log('📡 Received syncStarted event');
        });
        sync_manager_service_1.syncManager.on('syncCompleted', (result) => {
            events.push({ type: 'syncCompleted', data: result });
            console.log('📡 Received syncCompleted event:', result);
        });
        sync_manager_service_1.syncManager.on('syncError', (error) => {
            events.push({ type: 'syncError', data: error });
            console.log('📡 Received syncError event:', error.message);
        });
        sync_manager_service_1.syncManager.on('operationQueued', (operation) => {
            events.push({ type: 'operationQueued', data: operation });
            console.log('📡 Received operationQueued event:', operation);
        });
        // Test sync status retrieval
        console.log('\n📊 Testing sync status retrieval...');
        const syncStatus = sync_manager_service_1.syncManager.getSyncStatus();
        console.log('Current sync status:', syncStatus);
        const queueStatus = await sync_manager_service_1.syncManager.getQueueStatus();
        console.log('Current queue status:', queueStatus);
        const networkStatus = sync_manager_service_1.syncManager.getNetworkStatus();
        console.log('Current network status:', networkStatus);
        // Test operation queuing (this should trigger events)
        console.log('\n⚡ Testing operation queuing...');
        try {
            await sync_manager_service_1.syncManager.queueOperation('CREATE', 'todos', 'test-todo-' + Date.now(), { title: 'Test Todo', description: 'Test sync monitoring' }, 'test-user-id', 'high');
            console.log('✅ Operation queued successfully');
        }
        catch (error) {
            console.log('⚠️  Operation queuing failed (expected in test mode):', error.message);
        }
        // Wait a moment for events to be processed
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Test manual sync (this should trigger sync events)
        console.log('\n🔄 Testing manual sync...');
        try {
            const syncResult = await sync_manager_service_1.syncManager.performSync();
            console.log('✅ Manual sync completed:', syncResult);
        }
        catch (error) {
            console.log('⚠️  Manual sync failed (expected in test mode):', error.message);
        }
        // Wait for events to be processed
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('\n📈 Event Summary:');
        console.log(`Total events received: ${events.length}`);
        events.forEach((event, index) => {
            console.log(`  ${index + 1}. ${event.type}${event.data ? ' (with data)' : ''}`);
        });
        if (events.length > 0) {
            console.log('✅ Event emission test passed - events are being emitted correctly');
        }
        else {
            console.log('⚠️  Event emission test warning - no events received (may be expected in test mode)');
        }
    }
    async testStatusMonitoring() {
        console.log('\n🔍 Testing status monitoring capabilities...');
        // Test all status retrieval methods
        const tests = [
            { name: 'Sync Status', method: () => sync_manager_service_1.syncManager.getSyncStatus() },
            { name: 'Queue Status', method: () => sync_manager_service_1.syncManager.getQueueStatus() },
            { name: 'Network Status', method: () => sync_manager_service_1.syncManager.getNetworkStatus() },
            { name: 'Online Status', method: () => sync_manager_service_1.syncManager.isOnline() },
        ];
        for (const test of tests) {
            try {
                const result = await test.method();
                console.log(`✅ ${test.name}:`, result);
            }
            catch (error) {
                console.log(`❌ ${test.name} failed:`, error.message);
            }
        }
    }
    async cleanup() {
        console.log('\n🧹 Cleaning up test environment...');
        try {
            await sync_manager_service_1.syncManager.destroy();
            await this.databaseService?.close();
            await this.mcpService?.disconnect();
            console.log('✅ Cleanup completed');
        }
        catch (error) {
            console.error('❌ Cleanup error:', error);
        }
    }
    async run() {
        try {
            console.log('🚀 Starting Sync Status Monitoring Test\n');
            await this.initialize();
            await this.testEventEmission();
            await this.testStatusMonitoring();
            console.log('\n🎉 All tests completed successfully!');
        }
        catch (error) {
            console.error('\n💥 Test failed:', error);
            process.exit(1);
        }
        finally {
            await this.cleanup();
        }
    }
}
exports.SyncStatusMonitoringTest = SyncStatusMonitoringTest;
// Run the test if this file is executed directly
if (require.main === module) {
    const test = new SyncStatusMonitoringTest();
    test.run().then(() => {
        console.log('\n✨ Test execution finished');
        process.exit(0);
    }).catch((error) => {
        console.error('\n💥 Test execution failed:', error);
        process.exit(1);
    });
}
