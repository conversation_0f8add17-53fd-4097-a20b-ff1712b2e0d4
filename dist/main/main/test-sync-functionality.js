"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testSyncFunctionality = testSyncFunctionality;
const sync_manager_service_1 = require("./services/sync-manager.service");
const connection_1 = require("./database/connection");
const service_1 = require("./mcp/service");
async function testSyncFunctionality() {
    console.log('🧪 Testing Sync Functionality...\n');
    try {
        // Initialize database connection
        console.log('1. Initializing database connection...');
        await connection_1.dbConnection.initialize();
        console.log('✅ Database connection initialized\n');
        // Initialize MCP service
        console.log('2. Initializing MCP service...');
        try {
            await service_1.mcpService.initialize();
            console.log('✅ MCP service initialized');
        }
        catch (error) {
            console.log('⚠️  MCP service initialization failed (expected if no MCP servers configured)');
            console.log('   This is normal for local development without MotherDuck token');
        }
        console.log();
        // Initialize sync manager
        console.log('3. Initializing sync manager...');
        try {
            await sync_manager_service_1.syncManager.initialize();
            console.log('✅ Sync manager initialized\n');
            // Test sync status
            console.log('4. Testing sync status...');
            const syncStatus = sync_manager_service_1.syncManager.getSyncStatus();
            console.log('📊 Sync Status:', JSON.stringify(syncStatus, null, 2));
            // Test queue status
            console.log('\n5. Testing queue status...');
            const queueStatus = await sync_manager_service_1.syncManager.getQueueStatus();
            console.log('📊 Queue Status:', JSON.stringify(queueStatus, null, 2));
            // Test network status
            console.log('\n6. Testing network status...');
            const networkStatus = sync_manager_service_1.syncManager.getNetworkStatus();
            console.log('📊 Network Status:', JSON.stringify(networkStatus, null, 2));
            // Test queuing an operation
            console.log('\n7. Testing operation queuing...');
            const testTodo = {
                id: 'test-todo-123',
                title: 'Test Todo for Sync',
                description: 'This is a test todo for sync functionality',
                status: 'pending',
                priority: 'medium',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            await sync_manager_service_1.syncManager.queueOperation('CREATE', 'todos', testTodo.id, testTodo, 'test-user-123', 'medium');
            console.log('✅ Operation queued successfully');
            // Check queue status after queuing
            const updatedQueueStatus = await sync_manager_service_1.syncManager.getQueueStatus();
            console.log('📊 Updated Queue Status:', JSON.stringify(updatedQueueStatus, null, 2));
            // Test sync attempt (will fail if no MCP connection, but should handle gracefully)
            console.log('\n8. Testing sync attempt...');
            try {
                const syncResult = await sync_manager_service_1.syncManager.performSync();
                console.log('✅ Sync completed:', JSON.stringify(syncResult, null, 2));
            }
            catch (syncError) {
                console.log('⚠️  Sync failed (expected without MCP connection):', syncError instanceof Error ? syncError.message : String(syncError));
            }
            console.log('\n✅ All sync functionality tests completed successfully!');
        }
        catch (error) {
            console.log('⚠️  Sync manager initialization failed:', error instanceof Error ? error.message : String(error));
            console.log('   This is expected if MCP service is not available');
        }
    }
    catch (error) {
        console.error('❌ Test failed:', error);
    }
    finally {
        // Cleanup
        try {
            await sync_manager_service_1.syncManager.destroy();
            await connection_1.dbConnection.close();
            console.log('\n🧹 Cleanup completed');
        }
        catch (cleanupError) {
            console.warn('⚠️  Cleanup warning:', cleanupError);
        }
    }
}
// Run the test
if (require.main === module) {
    testSyncFunctionality()
        .then(() => {
        console.log('\n🎉 Sync functionality test completed!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('\n💥 Test failed with error:', error);
        process.exit(1);
    });
}
