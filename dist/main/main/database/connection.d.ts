import { QueryResult, ConnectionStatus } from '../../shared/types';
import { InitializationResult } from './schema-initializer';
export declare class DatabaseConnection {
    private static instance;
    private db;
    private connection;
    private isConnected;
    private connectionStatus;
    private initializationResult;
    private constructor();
    static getInstance(): DatabaseConnection;
    initialize(): Promise<void>;
    private connect;
    private configureDatabase;
    private connectToLocalDatabase;
    private connectToMotherDuck;
    /**
     * Initialize database schema for MotherDuck
     */
    private initializeDatabaseSchema;
    private testConnection;
    executeQuery<T = any>(query: string, params?: any[]): Promise<QueryResult<T>>;
    executeTransaction<T>(callback: (execute: (query: string, params?: any[]) => Promise<QueryResult>) => Promise<T>): Promise<T>;
    batch(queries: Array<{
        query: string;
        params?: any[];
    }>): Promise<QueryResult[]>;
    getConnectionStatus(): ConnectionStatus;
    isReady(): boolean;
    reconnect(): Promise<void>;
    close(): Promise<void>;
    healthCheck(): Promise<boolean>;
    getStats(): Promise<any>;
    isMotherDuckConnection(): boolean;
    getConnectionType(): 'local' | 'motherduck';
    getInitializationResult(): InitializationResult | null;
    getMotherDuckInfo(): Promise<any>;
    getInitializationHistory(): Promise<any[]>;
    forceReinitialize(includeSeedData?: boolean): Promise<InitializationResult>;
}
export declare const dbConnection: DatabaseConnection;
