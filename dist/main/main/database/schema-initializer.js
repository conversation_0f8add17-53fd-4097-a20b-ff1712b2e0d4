"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.schemaInitializer = exports.DatabaseSchemaInitializer = void 0;
const connection_1 = require("./connection");
const types_1 = require("../../shared/types");
class DatabaseSchemaInitializer {
    static SCHEMA_VERSION = '1.0.0';
    static INITIALIZATION_TABLE = '_motherduck_initialization';
    /**
     * Check if database needs initialization
     */
    async needsInitialization() {
        try {
            // Check if initialization tracking table exists
            const result = await connection_1.dbConnection.executeQuery(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_name = '${DatabaseSchemaInitializer.INITIALIZATION_TABLE}'
      `);
            if (result.rows[0].count === 0) {
                console.log('🔍 Initialization table not found - database needs initialization');
                return true;
            }
            // Check if initialization was completed
            const initResult = await connection_1.dbConnection.executeQuery(`
        SELECT schema_version, initialized_at, status 
        FROM ${DatabaseSchemaInitializer.INITIALIZATION_TABLE} 
        WHERE status = 'completed'
        ORDER BY initialized_at DESC 
        LIMIT 1
      `);
            if (initResult.rows.length === 0) {
                console.log('🔍 No completed initialization found - database needs initialization');
                return true;
            }
            const lastInit = initResult.rows[0];
            console.log(`✅ Database already initialized (version: ${lastInit.schema_version}, date: ${lastInit.initialized_at})`);
            return false;
        }
        catch (error) {
            console.log('🔍 Error checking initialization status - assuming database needs initialization:', error);
            return true;
        }
    }
    /**
     * Initialize database schema and seed data
     */
    async initializeDatabase(includeSeedData = true) {
        const startTime = Date.now();
        const tablesCreated = [];
        try {
            console.log('🚀 Starting MotherDuck database initialization...');
            // 1. Create initialization tracking table first
            await this.createInitializationTable();
            // 2. Record initialization start
            await this.recordInitializationStart();
            // 3. Create core application tables
            console.log('📋 Creating application tables...');
            const coreTablesCreated = await this.createCoreTables();
            tablesCreated.push(...coreTablesCreated);
            // 4. Create indexes for performance
            console.log('🔍 Creating database indexes...');
            await this.createIndexes();
            // 5. Insert seed data if requested
            let seedDataInserted = false;
            if (includeSeedData) {
                console.log('🌱 Inserting seed data...');
                await this.insertSeedData();
                seedDataInserted = true;
            }
            // 6. Record successful completion
            await this.recordInitializationComplete();
            const initializationTime = Date.now() - startTime;
            const result = {
                wasInitialized: true,
                tablesCreated,
                seedDataInserted,
                initializationTime,
                message: `Database initialized successfully in ${initializationTime}ms`
            };
            console.log(`✅ ${result.message}`);
            console.log(`📊 Tables created: ${tablesCreated.join(', ')}`);
            return result;
        }
        catch (error) {
            console.error('❌ Database initialization failed:', error);
            // Record failed initialization
            try {
                await this.recordInitializationError(error);
            }
            catch (recordError) {
                console.error('Failed to record initialization error:', recordError);
            }
            throw new types_1.DatabaseError('Database initialization failed', 'INIT_ERROR', error);
        }
    }
    /**
     * Create initialization tracking table
     */
    async createInitializationTable() {
        await connection_1.dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS ${DatabaseSchemaInitializer.INITIALIZATION_TABLE} (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        schema_version VARCHAR NOT NULL,
        status VARCHAR NOT NULL CHECK (status IN ('started', 'completed', 'failed')),
        initialized_at TIMESTAMP DEFAULT current_timestamp,
        completed_at TIMESTAMP,
        error_message TEXT,
        tables_created VARCHAR[],
        seed_data_inserted BOOLEAN DEFAULT false
      )
    `);
    }
    /**
     * Create core application tables
     */
    async createCoreTables() {
        const tables = [];
        // Users table
        await connection_1.dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR UNIQUE NOT NULL,
        name VARCHAR NOT NULL,
        password_hash VARCHAR NOT NULL,
        avatar_url VARCHAR,
        preferences JSON DEFAULT '{}',
        created_at TIMESTAMP DEFAULT current_timestamp,
        updated_at TIMESTAMP DEFAULT current_timestamp,
        last_login_at TIMESTAMP
      )
    `);
        tables.push('users');
        // Categories table
        await connection_1.dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR UNIQUE NOT NULL,
        description TEXT,
        color VARCHAR DEFAULT '#3B82F6',
        icon VARCHAR DEFAULT 'folder',
        created_at TIMESTAMP DEFAULT current_timestamp,
        updated_at TIMESTAMP DEFAULT current_timestamp
      )
    `);
        tables.push('categories');
        // Todos table
        await connection_1.dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS todos (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        title VARCHAR NOT NULL,
        description TEXT,
        completed BOOLEAN DEFAULT false,
        priority VARCHAR CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
        due_date TIMESTAMP,
        completed_at TIMESTAMP,
        metadata JSON DEFAULT '{}',
        created_at TIMESTAMP DEFAULT current_timestamp,
        updated_at TIMESTAMP DEFAULT current_timestamp,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )
    `);
        tables.push('todos');
        // Todo-Categories junction table
        await connection_1.dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS todo_categories (
        todo_id UUID NOT NULL,
        category_id UUID NOT NULL,
        assigned_at TIMESTAMP DEFAULT current_timestamp,
        PRIMARY KEY (todo_id, category_id),
        FOREIGN KEY (todo_id) REFERENCES todos(id),
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )
    `);
        tables.push('todo_categories');
        // User sessions table (for authentication)
        await connection_1.dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        session_token VARCHAR UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT current_timestamp,
        last_accessed_at TIMESTAMP DEFAULT current_timestamp,
        ip_address VARCHAR,
        user_agent TEXT,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )
    `);
        tables.push('user_sessions');
        return tables;
    }
    /**
     * Create database indexes for performance
     */
    async createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_todos_user_id ON todos(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_todos_completed ON todos(completed)',
            'CREATE INDEX IF NOT EXISTS idx_todos_priority ON todos(priority)',
            'CREATE INDEX IF NOT EXISTS idx_todos_due_date ON todos(due_date)',
            'CREATE INDEX IF NOT EXISTS idx_todos_created_at ON todos(created_at)',
            'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)',
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at)',
            'CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)'
        ];
        for (const indexSql of indexes) {
            try {
                await connection_1.dbConnection.executeQuery(indexSql);
            }
            catch (error) {
                console.warn(`Failed to create index: ${indexSql}`, error);
            }
        }
    }
    /**
     * Insert seed data for development and testing
     */
    async insertSeedData() {
        // Insert default categories (check if they exist first)
        const existingCategories = await connection_1.dbConnection.executeQuery(`
      SELECT name FROM categories WHERE name IN ('Work', 'Personal', 'Learning', 'Health', 'Shopping', 'Travel')
    `);
        const existingCategoryNames = existingCategories.rows.map(row => row.name);
        const categoriesToInsert = [
            { name: 'Work', description: 'Work-related tasks and projects', color: '#EF4444', icon: 'briefcase' },
            { name: 'Personal', description: 'Personal tasks and reminders', color: '#10B981', icon: 'user' },
            { name: 'Learning', description: 'Educational and skill development tasks', color: '#8B5CF6', icon: 'book' },
            { name: 'Health', description: 'Health and fitness related tasks', color: '#F59E0B', icon: 'heart' },
            { name: 'Shopping', description: 'Shopping lists and purchases', color: '#06B6D4', icon: 'shopping-cart' },
            { name: 'Travel', description: 'Travel planning and arrangements', color: '#84CC16', icon: 'map' }
        ].filter(cat => !existingCategoryNames.includes(cat.name));
        if (categoriesToInsert.length > 0) {
            const values = categoriesToInsert.map(cat => `('${cat.name}', '${cat.description}', '${cat.color}', '${cat.icon}')`).join(', ');
            await connection_1.dbConnection.executeQuery(`
        INSERT INTO categories (name, description, color, icon)
        VALUES ${values}
      `);
            console.log(`Inserted ${categoriesToInsert.length} new categories`);
        }
        else {
            console.log('All default categories already exist');
        }
        // Insert demo user (check if exists first)
        let demoUserResult = await connection_1.dbConnection.executeQuery(`
      SELECT id FROM users WHERE email = '<EMAIL>'
    `);
        let demoUserId;
        if (demoUserResult.rows.length === 0) {
            // Insert new demo user
            demoUserResult = await connection_1.dbConnection.executeQuery(`
        INSERT INTO users (email, name, password_hash, preferences)
        VALUES (
          '<EMAIL>',
          'Demo User',
          'demo_password_hash',
          '{"theme": "light", "notifications": true, "timezone": "UTC"}'
        )
        RETURNING id
      `);
            demoUserId = demoUserResult.rows[0].id;
        }
        else {
            demoUserId = demoUserResult.rows[0].id;
            console.log('Demo user already exists, using existing user');
        }
        if (demoUserId) {
            // Get category IDs for seed todos
            const categoriesResult = await connection_1.dbConnection.executeQuery(`
        SELECT id, name FROM categories WHERE name IN ('Work', 'Personal', 'Learning')
      `);
            const categoryMap = new Map();
            categoriesResult.rows.forEach(cat => categoryMap.set(cat.name, cat.id));
            // Check if demo todos already exist
            const existingTodos = await connection_1.dbConnection.executeQuery(`
        SELECT COUNT(*) as count FROM todos
        WHERE user_id = '${demoUserId}' AND metadata::json->>'source' = 'seed_data'
      `);
            if (existingTodos.rows[0].count === 0) {
                // Insert demo todos
                await connection_1.dbConnection.executeQuery(`
          INSERT INTO todos (user_id, title, description, priority, due_date, completed, metadata)
          VALUES
            (
              '${demoUserId}',
              'Welcome to MotherDuck!',
              'This is your first todo in the cloud database. Try editing, completing, or creating new todos.',
              'high',
              current_timestamp + INTERVAL '1 day',
              false,
              '{"source": "seed_data", "tutorial": true}'
            ),
            (
              '${demoUserId}',
              'Explore MotherDuck Features',
              'Learn about cloud database capabilities, sharing, and collaboration features.',
              'medium',
              current_timestamp + INTERVAL '3 days',
              false,
              '{"source": "seed_data", "tutorial": true}'
            ),
            (
              '${demoUserId}',
              'Setup Complete',
              'Your MotherDuck database has been successfully initialized with sample data.',
              'low',
              NULL,
              true,
              '{"source": "seed_data", "completed_during_init": true}'
            )
        `);
                console.log('Inserted demo todos');
            }
            else {
                console.log('Demo todos already exist');
            }
            // Assign categories to todos if categories exist
            if (categoryMap.size > 0) {
                const todosResult = await connection_1.dbConnection.executeQuery(`
          SELECT id FROM todos WHERE user_id = '${demoUserId}' ORDER BY created_at LIMIT 3
        `);
                if (todosResult.rows.length >= 3) {
                    const workCatId = categoryMap.get('Work');
                    const personalCatId = categoryMap.get('Personal');
                    const learningCatId = categoryMap.get('Learning');
                    if (workCatId && personalCatId && learningCatId) {
                        await connection_1.dbConnection.executeQuery(`
              INSERT INTO todo_categories (todo_id, category_id)
              VALUES 
                ('${todosResult.rows[0].id}', '${workCatId}'),
                ('${todosResult.rows[1].id}', '${learningCatId}'),
                ('${todosResult.rows[2].id}', '${personalCatId}')
            `);
                    }
                }
            }
        }
    }
    /**
     * Record initialization start
     */
    async recordInitializationStart() {
        await connection_1.dbConnection.executeQuery(`
      INSERT INTO ${DatabaseSchemaInitializer.INITIALIZATION_TABLE} 
      (schema_version, status)
      VALUES ('${DatabaseSchemaInitializer.SCHEMA_VERSION}', 'started')
    `);
    }
    /**
     * Record successful initialization completion
     */
    async recordInitializationComplete() {
        await connection_1.dbConnection.executeQuery(`
      UPDATE ${DatabaseSchemaInitializer.INITIALIZATION_TABLE}
      SET 
        status = 'completed',
        completed_at = current_timestamp,
        tables_created = ARRAY['users', 'categories', 'todos', 'todo_categories', 'user_sessions'],
        seed_data_inserted = true
      WHERE status = 'started'
        AND schema_version = '${DatabaseSchemaInitializer.SCHEMA_VERSION}'
    `);
    }
    /**
     * Record initialization error
     */
    async recordInitializationError(error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        await connection_1.dbConnection.executeQuery(`
      UPDATE ${DatabaseSchemaInitializer.INITIALIZATION_TABLE}
      SET 
        status = 'failed',
        completed_at = current_timestamp,
        error_message = '${errorMessage.replace(/'/g, "''")}'
      WHERE status = 'started'
        AND schema_version = '${DatabaseSchemaInitializer.SCHEMA_VERSION}'
    `);
    }
    /**
     * Get initialization history
     */
    async getInitializationHistory() {
        const result = await connection_1.dbConnection.executeQuery(`
      SELECT 
        schema_version,
        status,
        initialized_at,
        completed_at,
        error_message,
        tables_created,
        seed_data_inserted
      FROM ${DatabaseSchemaInitializer.INITIALIZATION_TABLE}
      ORDER BY initialized_at DESC
    `);
        return result.rows;
    }
}
exports.DatabaseSchemaInitializer = DatabaseSchemaInitializer;
// Export singleton instance
exports.schemaInitializer = new DatabaseSchemaInitializer();
