"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MotherDuckConnectionTester = void 0;
const connection_1 = require("./connection");
const config_1 = require("../utils/config");
class MotherDuckConnectionTester {
    results = [];
    addResult(name, status, message, details) {
        const result = {
            name,
            status,
            message,
            details,
            timestamp: new Date()
        };
        this.results.push(result);
        const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : status === 'WARN' ? '⚠️' : '⏭️';
        console.log(`${icon} ${name}: ${message}`);
        if (details) {
            console.log(`   Details:`, JSON.stringify(details, null, 2));
        }
    }
    async runAllTests() {
        console.log('🦆 MotherDuck Connection Test');
        console.log('=============================\n');
        try {
            await this.testConfiguration();
            await this.testConnection();
            await this.testMotherDuckFeatures();
            await this.testDatabaseOperations();
            await this.testSyncCapabilities();
            this.printSummary();
        }
        catch (error) {
            console.error('❌ MotherDuck test suite failed:', error);
            this.addResult('Test Suite', 'FAIL', `Critical error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testConfiguration() {
        console.log('1. Testing MotherDuck Configuration...');
        try {
            const dbConfig = config_1.config.getDatabaseConfig();
            if (!dbConfig.motherduckToken) {
                this.addResult('Configuration Check', 'SKIP', 'No MotherDuck token configured - will use local database', {
                    hasToken: false,
                    databasePath: dbConfig.path
                });
                return;
            }
            this.addResult('Configuration Check', 'PASS', 'MotherDuck token configured', {
                hasToken: true,
                tokenLength: dbConfig.motherduckToken.length,
                databaseName: process.env.DATABASE_NAME || 'todo_app'
            });
        }
        catch (error) {
            this.addResult('Configuration Check', 'FAIL', `Configuration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testConnection() {
        console.log('\n2. Testing Database Connection...');
        try {
            await connection_1.dbConnection.initialize();
            const connectionType = connection_1.dbConnection.getConnectionType();
            const isHealthy = await connection_1.dbConnection.healthCheck();
            this.addResult('Database Connection', isHealthy ? 'PASS' : 'FAIL', `Connected to ${connectionType} database`, {
                connectionType,
                isHealthy,
                isReady: connection_1.dbConnection.isReady()
            });
        }
        catch (error) {
            this.addResult('Database Connection', 'FAIL', `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testMotherDuckFeatures() {
        console.log('\n3. Testing MotherDuck Features...');
        try {
            if (!connection_1.dbConnection.isMotherDuckConnection()) {
                this.addResult('MotherDuck Features', 'SKIP', 'Not connected to MotherDuck - skipping cloud features');
                return;
            }
            const motherDuckInfo = await connection_1.dbConnection.getMotherDuckInfo();
            this.addResult('MotherDuck Features', 'PASS', 'Successfully retrieved MotherDuck information', motherDuckInfo);
        }
        catch (error) {
            this.addResult('MotherDuck Features', 'FAIL', `MotherDuck features test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testDatabaseOperations() {
        console.log('\n4. Testing Database Operations...');
        try {
            // Test basic query
            const testQuery = await connection_1.dbConnection.executeQuery('SELECT 1 as test_value, current_timestamp as test_time');
            // Test table creation (temporary)
            await connection_1.dbConnection.executeQuery(`
        CREATE TEMPORARY TABLE test_motherduck_connection (
          id INTEGER,
          name VARCHAR,
          created_at TIMESTAMP DEFAULT current_timestamp
        )
      `);
            // Test data insertion
            await connection_1.dbConnection.executeQuery(`
        INSERT INTO test_motherduck_connection (id, name) 
        VALUES (1, 'MotherDuck Test'), (2, 'Connection Test')
      `);
            // Test data retrieval
            const testData = await connection_1.dbConnection.executeQuery('SELECT * FROM test_motherduck_connection ORDER BY id');
            this.addResult('Database Operations', 'PASS', 'All database operations completed successfully', {
                testQueryResult: testQuery.rows[0],
                testDataCount: testData.rowCount,
                testData: testData.rows
            });
        }
        catch (error) {
            this.addResult('Database Operations', 'FAIL', `Database operations failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testSyncCapabilities() {
        console.log('\n5. Testing Sync Capabilities...');
        try {
            if (!connection_1.dbConnection.isMotherDuckConnection()) {
                this.addResult('Sync Capabilities', 'SKIP', 'Not connected to MotherDuck - no sync capabilities');
                return;
            }
            // Test basic MotherDuck functionality
            await connection_1.dbConnection.executeQuery('SELECT 1 as test');
            this.addResult('Sync Capabilities', 'PASS', 'MotherDuck sync test completed successfully');
        }
        catch (error) {
            this.addResult('Sync Capabilities', 'WARN', `Sync test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    printSummary() {
        console.log('\n📊 MotherDuck Test Summary');
        console.log('==========================');
        const passed = this.results.filter(r => r.status === 'PASS').length;
        const failed = this.results.filter(r => r.status === 'FAIL').length;
        const warned = this.results.filter(r => r.status === 'WARN').length;
        const skipped = this.results.filter(r => r.status === 'SKIP').length;
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`⚠️  Warnings: ${warned}`);
        console.log(`⏭️  Skipped: ${skipped}`);
        console.log(`📝 Total: ${this.results.length}`);
        const connectionType = connection_1.dbConnection.getConnectionType();
        if (failed === 0) {
            if (connectionType === 'motherduck') {
                console.log('\n🎉 MotherDuck connection is working perfectly!');
            }
            else {
                console.log('\n✅ Local database connection is working. Add MOTHERDUCK_TOKEN to test cloud features.');
            }
        }
        else {
            console.log('\n⚠️  Some tests failed. Check the details above.');
        }
        console.log(`\n🔗 Connection Type: ${connectionType}`);
    }
}
exports.MotherDuckConnectionTester = MotherDuckConnectionTester;
// Run the tests if this file is executed directly
if (require.main === module) {
    const tester = new MotherDuckConnectionTester();
    tester.runAllTests()
        .then(() => {
        process.exit(0);
    })
        .catch((error) => {
        console.error('Test execution failed:', error);
        process.exit(1);
    });
}
