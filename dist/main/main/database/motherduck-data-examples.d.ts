/**
 * MotherDuck Data Insertion Examples
 *
 * This file demonstrates how to create tables and insert data into MotherDuck.
 * The syntax is identical to local DuckDB - MotherDuck is fully compatible!
 */
export declare class MotherDuckDataExamples {
    /**
     * 1. CREATE TABLES
     * MotherDuck supports all standard DuckDB table creation syntax
     */
    createTodoTables(): Promise<void>;
    /**
     * 2. INSERT SINGLE RECORDS
     * Standard INSERT syntax works exactly the same
     */
    insertSampleUser(): Promise<string>;
    /**
     * 3. INSERT MULTIPLE RECORDS
     * Batch inserts for better performance
     */
    insertSampleTodos(userId: string): Promise<void>;
    /**
     * 4. INSERT WITH PARAMETERIZED QUERIES (Safer approach)
     * Note: DuckDB Node.js doesn't support parameterized queries directly,
     * but you can use string interpolation safely with proper validation
     */
    insertTodoSafely(userId: string, title: string, description: string, priority: 'low' | 'medium' | 'high'): Promise<string>;
    /**
     * 5. INSERT CATEGORIES AND RELATIONSHIPS
     */
    insertCategoriesAndAssignments(): Promise<void>;
    /**
     * 6. <PERSON><PERSON><PERSON><PERSON> INSERT FROM DATA ARRAYS
     * Useful for importing large datasets
     */
    bulkInsertTodos(userId: string, todosData: Array<{
        title: string;
        description: string;
        priority: string;
    }>): Promise<void>;
    /**
     * 7. INSERT WITH COMPLEX DATA TYPES
     * MotherDuck supports JSON, arrays, and other complex types
     */
    insertTodoWithMetadata(): Promise<void>;
    /**
     * 8. QUERY DATA TO VERIFY INSERTIONS
     */
    queryInsertedData(): Promise<void>;
    /**
     * 9. RUN ALL EXAMPLES
     */
    runAllExamples(): Promise<void>;
}
export declare const motherDuckExamples: MotherDuckDataExamples;
