"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.simpleMotherDuckExamples = exports.SimpleMotherDuckExamples = void 0;
const connection_1 = require("./connection");
/**
 * Simple MotherDuck Data Insertion Examples
 *
 * This demonstrates the basic syntax for inserting data into MotherDuck.
 * MotherDuck uses standard DuckDB SQL syntax - no differences!
 */
class SimpleMotherDuckExamples {
    async runSimpleExamples() {
        try {
            console.log('🚀 Simple MotherDuck Data Insertion Examples\n');
            // Initialize database connection
            console.log('Initializing database connection...');
            await connection_1.dbConnection.initialize();
            console.log('✅ Connected to MotherDuck\n');
            // 1. CREATE A SIMPLE TABLE
            console.log('1. Creating a simple table...');
            await connection_1.dbConnection.executeQuery(`
        CREATE TABLE IF NOT EXISTS demo_tasks (
          id INTEGER PRIMARY KEY,
          title VARCHAR NOT NULL,
          description TEXT,
          priority VARCHAR DEFAULT 'medium',
          completed BOOLEAN DEFAULT false,
          created_at TIMESTAMP DEFAULT current_timestamp
        )
      `);
            console.log('✅ Table "demo_tasks" created\n');
            // 2. INSERT SINGLE RECORD
            console.log('2. Inserting a single record...');
            await connection_1.dbConnection.executeQuery(`
        INSERT INTO demo_tasks (id, title, description, priority)
        VALUES (1, 'Learn MotherDuck', 'Understand how to insert data into MotherDuck', 'high')
      `);
            console.log('✅ Single record inserted\n');
            // 3. INSERT MULTIPLE RECORDS
            console.log('3. Inserting multiple records...');
            await connection_1.dbConnection.executeQuery(`
        INSERT INTO demo_tasks (id, title, description, priority, completed)
        VALUES 
          (2, 'Setup Database', 'Configure MotherDuck connection', 'high', true),
          (3, 'Write Tests', 'Create unit tests for the application', 'medium', false),
          (4, 'Deploy App', 'Deploy to production environment', 'low', false),
          (5, 'Documentation', 'Write user documentation', 'medium', false)
      `);
            console.log('✅ Multiple records inserted\n');
            // 4. INSERT WITH CURRENT TIMESTAMP
            console.log('4. Inserting with current timestamp...');
            await connection_1.dbConnection.executeQuery(`
        INSERT INTO demo_tasks (id, title, description, created_at)
        VALUES (6, 'Timestamp Test', 'Testing timestamp insertion', current_timestamp)
      `);
            console.log('✅ Record with timestamp inserted\n');
            // 5. INSERT WITH STRING ESCAPING
            console.log('5. Inserting with special characters...');
            const titleWithQuotes = "Task with 'single quotes' and \"double quotes\"";
            const descriptionWithQuotes = "Description with 'quotes' and special chars: @#$%";
            // Escape single quotes by doubling them
            const safeTitleWithQuotes = titleWithQuotes.replace(/'/g, "''");
            const safeDescriptionWithQuotes = descriptionWithQuotes.replace(/'/g, "''");
            await connection_1.dbConnection.executeQuery(`
        INSERT INTO demo_tasks (id, title, description)
        VALUES (7, '${safeTitleWithQuotes}', '${safeDescriptionWithQuotes}')
      `);
            console.log('✅ Record with special characters inserted\n');
            // 6. INSERT AND RETURN DATA
            console.log('6. Inserting and returning data...');
            const result = await connection_1.dbConnection.executeQuery(`
        INSERT INTO demo_tasks (id, title, description, priority)
        VALUES (8, 'Return Test', 'Testing RETURNING clause', 'high')
        RETURNING id, title, created_at
      `);
            console.log('✅ Inserted and returned:', result.rows[0]);
            console.log('');
            // 7. QUERY ALL DATA
            console.log('7. Querying all inserted data...');
            const allTasks = await connection_1.dbConnection.executeQuery(`
        SELECT 
          id,
          title,
          description,
          priority,
          completed,
          created_at
        FROM demo_tasks 
        ORDER BY id
      `);
            console.log(`📊 Total tasks: ${allTasks.rowCount}`);
            console.log('📝 All tasks:');
            allTasks.rows.forEach(task => {
                const status = task.completed ? '✅' : '⏳';
                const priority = task.priority.toUpperCase();
                console.log(`   ${status} [${priority}] ${task.title}`);
            });
            console.log('');
            // 8. UPDATE DATA
            console.log('8. Updating data...');
            await connection_1.dbConnection.executeQuery(`
        UPDATE demo_tasks 
        SET completed = true, priority = 'high'
        WHERE id = 3
      `);
            console.log('✅ Task updated\n');
            // 9. DELETE DATA
            console.log('9. Deleting data...');
            await connection_1.dbConnection.executeQuery(`
        DELETE FROM demo_tasks 
        WHERE id = 7
      `);
            console.log('✅ Task deleted\n');
            // 10. ADVANCED QUERIES
            console.log('10. Running advanced queries...');
            // Count by priority
            const priorityCount = await connection_1.dbConnection.executeQuery(`
        SELECT 
          priority,
          COUNT(*) as count,
          COUNT(CASE WHEN completed THEN 1 END) as completed_count
        FROM demo_tasks 
        GROUP BY priority
        ORDER BY priority
      `);
            console.log('📊 Tasks by priority:');
            priorityCount.rows.forEach(row => {
                console.log(`   ${row.priority}: ${row.count} total, ${row.completed_count} completed`);
            });
            console.log('');
            // Recent tasks
            const recentTasks = await connection_1.dbConnection.executeQuery(`
        SELECT title, created_at
        FROM demo_tasks 
        WHERE created_at >= current_timestamp - INTERVAL '1 hour'
        ORDER BY created_at DESC
        LIMIT 3
      `);
            console.log('🕒 Recent tasks (last hour):');
            recentTasks.rows.forEach(task => {
                console.log(`   - ${task.title} (${task.created_at})`);
            });
            console.log('');
            // 11. BULK INSERT FROM ARRAY
            console.log('11. Bulk insert from data array...');
            const bulkData = [
                { id: 10, title: 'Bulk Task 1', priority: 'low' },
                { id: 11, title: 'Bulk Task 2', priority: 'medium' },
                { id: 12, title: 'Bulk Task 3', priority: 'high' }
            ];
            const values = bulkData.map(task => `(${task.id}, '${task.title}', '${task.priority}')`).join(', ');
            await connection_1.dbConnection.executeQuery(`
        INSERT INTO demo_tasks (id, title, priority)
        VALUES ${values}
      `);
            console.log('✅ Bulk insert completed\n');
            // 12. FINAL COUNT
            const finalCount = await connection_1.dbConnection.executeQuery(`
        SELECT COUNT(*) as total_count FROM demo_tasks
      `);
            console.log(`🎯 Final total: ${finalCount.rows[0].total_count} tasks in MotherDuck database\n`);
            console.log('🎉 All MotherDuck data insertion examples completed successfully!');
            console.log('\n📋 Key Points:');
            console.log('   • MotherDuck uses standard DuckDB SQL syntax');
            console.log('   • No differences between local DuckDB and MotherDuck for data operations');
            console.log('   • All standard SQL operations work: INSERT, UPDATE, DELETE, SELECT');
            console.log('   • Foreign keys, constraints, and advanced features are supported');
            console.log('   • Data is automatically synced to the cloud');
        }
        catch (error) {
            console.error('❌ Error running examples:', error);
            throw error;
        }
    }
}
exports.SimpleMotherDuckExamples = SimpleMotherDuckExamples;
// Export for use in other files
exports.simpleMotherDuckExamples = new SimpleMotherDuckExamples();
// Run examples if this file is executed directly
if (require.main === module) {
    exports.simpleMotherDuckExamples.runSimpleExamples()
        .then(() => {
        console.log('\nExamples completed successfully');
        process.exit(0);
    })
        .catch((error) => {
        console.error('Examples failed:', error);
        process.exit(1);
    });
}
