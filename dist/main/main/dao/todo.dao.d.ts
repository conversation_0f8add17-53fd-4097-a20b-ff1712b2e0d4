import { OfflineFirstBaseDAO, OfflineFirstOptions } from './offline-first-base.dao';
import { Todo, TodoStatus, TodoPriority, FilterOptions, PaginationOptions } from '../../shared/types';
export interface TodoFilterOptions extends FilterOptions {
    status?: TodoStatus;
    priority?: TodoPriority;
    category_id?: string;
    due_date_from?: Date;
    due_date_to?: Date;
    tags?: string[];
    search?: string;
    is_completed?: boolean;
    is_overdue?: boolean;
}
export declare class TodoDAO extends OfflineFirstBaseDAO<Todo> {
    constructor();
    /**
     * Create a new todo with offline-first strategy
     */
    createTodoOfflineFirst(todoData: Partial<Todo>, userId: string, options?: OfflineFirstOptions): Promise<Todo>;
    /**
     * Create a new todo with proper validation and defaults (legacy method)
     */
    createTodo(todoData: Partial<Todo>, userId: string): Promise<Todo>;
    /**
     * Find todo by ID with user authorization (offline-first)
     */
    findByIdForUserOfflineFirst(id: string, userId: string, options?: OfflineFirstOptions): Promise<Todo | null>;
    /**
     * Find todo by ID with user authorization (legacy method)
     */
    findByIdForUser(id: string, userId: string): Promise<Todo | null>;
    /**
     * Update todo with user authorization and special handling
     */
    updateTodo(id: string, data: Partial<Todo>, userId: string): Promise<Todo>;
    /**
     * Delete todo with user authorization
     */
    deleteTodo(id: string, userId: string): Promise<boolean>;
    /**
     * Soft delete todo with user authorization
     */
    softDeleteTodo(id: string, userId: string): Promise<Todo>;
    /**
     * Find todos by user ID with offline-first strategy
     */
    findByUserIdOfflineFirst(userId: string, pagination?: PaginationOptions, filters?: TodoFilterOptions, options?: OfflineFirstOptions): Promise<{
        todos: Todo[];
        total: number;
        hasMore: boolean;
    }>;
    findByUserId(userId: string, pagination?: PaginationOptions, filters?: TodoFilterOptions): Promise<{
        data: Todo[];
        total: number;
    }>;
    /**
     * Update todo status with offline-first strategy
     */
    updateStatusOfflineFirst(todoId: string, status: TodoStatus, userId: string, options?: OfflineFirstOptions): Promise<Todo>;
    updateStatus(todoId: string, status: TodoStatus, userId: string): Promise<Todo>;
    /**
     * Soft delete todo with offline-first strategy
     */
    softDeleteTodoOfflineFirst(todoId: string, userId: string, options?: OfflineFirstOptions): Promise<Todo>;
    updatePriority(todoId: string, priority: TodoPriority, userId: string): Promise<Todo>;
    updatePosition(todoId: string, newPosition: number, userId: string): Promise<Todo>;
    addTags(todoId: string, tags: string[], userId: string): Promise<Todo>;
    removeTags(todoId: string, tags: string[], userId: string): Promise<Todo>;
    getOverdueTodos(userId: string): Promise<Todo[]>;
    getTodosWithReminders(beforeTime?: Date): Promise<Todo[]>;
    getTodosByCategory(categoryId: string, userId: string): Promise<Todo[]>;
    searchTodos(userId: string, searchTerm: string, limit?: number): Promise<Todo[]>;
    getUserTodoStats(userId: string): Promise<{
        total: number;
        completed: number;
        pending: number;
        inProgress: number;
        overdue: number;
        completionRate: number;
    }>;
    getNextPosition(categoryId: string | null, userId: string): Promise<number>;
    moveToCategory(todoId: string, newCategoryId: string | null, userId: string): Promise<Todo>;
    getAllTags(userId: string): Promise<{
        tag: string;
        count: number;
        completed_count: number;
    }[]>;
    getTagSuggestions(userId: string, query: string, limit?: number): Promise<string[]>;
    getPopularTags(userId: string, limit?: number): Promise<string[]>;
    getRecentTags(userId: string, limit?: number): Promise<string[]>;
    getTagStats(userId: string, tag: string): Promise<{
        total_count: number;
        completed_count: number;
        pending_count: number;
        last_used: Date | null;
    } | null>;
}
export declare const todoDAO: TodoDAO;
