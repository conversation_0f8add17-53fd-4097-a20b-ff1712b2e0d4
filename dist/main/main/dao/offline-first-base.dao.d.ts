import { BaseDAO } from './base.dao';
import { QueryResult, PaginationOptions, FilterOptions } from '../../shared/types';
export interface OfflineFirstOptions {
    useCache?: boolean;
    forceRefresh?: boolean;
    enableOptimisticUpdates?: boolean;
    priority?: 'high' | 'medium' | 'low';
}
/**
 * Enhanced Base DAO with Offline-First capabilities
 *
 * This extends the base DAO to provide:
 * - Offline-first data access
 * - Optimistic updates
 * - Local caching
 * - Background synchronization
 * - Graceful degradation when offline
 */
export declare abstract class OfflineFirstBaseDAO<T> extends BaseDAO<T> {
    constructor(tableName: string);
    /**
     * Enhanced find method with offline-first strategy
     */
    findOfflineFirst(query: string, params?: any[], options?: OfflineFirstOptions): Promise<QueryResult<T>>;
    /**
     * Enhanced create method with offline-first strategy
     */
    createOfflineFirst(data: Partial<T>, userId: string, options?: OfflineFirstOptions): Promise<T>;
    /**
     * Enhanced update method with offline-first strategy
     */
    updateOfflineFirst(id: string, updates: Partial<T>, userId: string, options?: OfflineFirstOptions): Promise<T>;
    /**
     * Enhanced delete method with offline-first strategy
     */
    deleteOfflineFirst(id: string, userId: string, options?: OfflineFirstOptions): Promise<boolean>;
    /**
     * Enhanced soft delete method with offline-first strategy
     */
    softDeleteOfflineFirst(id: string, userId: string, options?: OfflineFirstOptions): Promise<T>;
    /**
     * Find by ID with offline-first strategy
     */
    findByIdOfflineFirst(id: string, options?: OfflineFirstOptions): Promise<T | null>;
    /**
     * Find all with offline-first strategy and pagination
     */
    findAllOfflineFirst(pagination?: PaginationOptions, filters?: FilterOptions, options?: OfflineFirstOptions): Promise<{
        data: T[];
        total: number;
        hasMore: boolean;
    }>;
    /**
     * Check if record exists with offline-first strategy
     */
    existsOfflineFirst(id: string, options?: OfflineFirstOptions): Promise<boolean>;
    /**
     * Get offline status for this DAO
     */
    getOfflineStatus(): {
        isOfflineCapable: boolean;
        isOnline: boolean;
        pendingOperations: number;
        lastSyncAt: Date | null;
        localDatabaseStatus: import("../../shared/types").ConnectionStatus;
        cacheSize: number;
        networkQuality: "excellent" | "good" | "poor" | "offline";
        tableName: string;
    };
    /**
     * Force sync pending operations for this table
     */
    forceSyncTable(): Promise<void>;
    /**
     * Clear cache for this table
     */
    clearTableCache(): void;
}
