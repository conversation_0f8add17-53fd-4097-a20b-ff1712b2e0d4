"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfflineFirstBaseDAO = void 0;
const base_dao_1 = require("./base.dao");
const offline_first_data_service_1 = require("../services/offline-first-data.service");
const types_1 = require("../../shared/types");
/**
 * Enhanced Base DAO with Offline-First capabilities
 *
 * This extends the base DAO to provide:
 * - Offline-first data access
 * - Optimistic updates
 * - Local caching
 * - Background synchronization
 * - Graceful degradation when offline
 */
class OfflineFirstBaseDAO extends base_dao_1.BaseDAO {
    constructor(tableName) {
        super(tableName);
    }
    /**
     * Enhanced find method with offline-first strategy
     */
    async findOfflineFirst(query, params = [], options = {}) {
        try {
            return await offline_first_data_service_1.offlineFirstDataService.getData(this.tableName, query, params, {
                useCache: options.useCache ?? true,
                forceRefresh: options.forceRefresh ?? false
            });
        }
        catch (error) {
            console.error(`Offline-first query failed for ${this.tableName}:`, error);
            throw error;
        }
    }
    /**
     * Enhanced create method with offline-first strategy
     */
    async createOfflineFirst(data, userId, options = {}) {
        const fields = Object.keys(data).filter(key => data[key] !== undefined);
        const values = fields.map(key => data[key]);
        const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
        const query = `
      INSERT INTO ${this.tableName} (${fields.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `;
        // Generate optimistic data for immediate UI feedback
        const optimisticData = options.enableOptimisticUpdates !== false ? {
            id: crypto.randomUUID(), // Temporary ID
            ...data,
            created_at: new Date(),
            updated_at: new Date()
        } : undefined;
        try {
            const result = await offline_first_data_service_1.offlineFirstDataService.mutateData('CREATE', this.tableName, query, values, optimisticData?.id || crypto.randomUUID(), userId, optimisticData);
            if (result.rows.length === 0) {
                throw new types_1.DatabaseError(`Failed to create record in ${this.tableName}`, 'CREATE_ERROR');
            }
            return result.rows[0];
        }
        catch (error) {
            console.error(`Offline-first create failed for ${this.tableName}:`, error);
            throw error;
        }
    }
    /**
     * Enhanced update method with offline-first strategy
     */
    async updateOfflineFirst(id, updates, userId, options = {}) {
        const fields = Object.keys(updates).filter(key => updates[key] !== undefined);
        const values = fields.map(key => updates[key]);
        const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');
        const query = `
      UPDATE ${this.tableName} 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${fields.length + 1}
      RETURNING *
    `;
        const queryParams = [...values, id];
        // Generate optimistic data
        const optimisticData = options.enableOptimisticUpdates !== false ? {
            id,
            ...updates,
            updated_at: new Date()
        } : undefined;
        try {
            const result = await offline_first_data_service_1.offlineFirstDataService.mutateData('UPDATE', this.tableName, query, queryParams, id, userId, optimisticData);
            if (result.rows.length === 0) {
                throw new types_1.DatabaseError(`Record not found in ${this.tableName}`, 'NOT_FOUND');
            }
            return result.rows[0];
        }
        catch (error) {
            console.error(`Offline-first update failed for ${this.tableName}:`, error);
            throw error;
        }
    }
    /**
     * Enhanced delete method with offline-first strategy
     */
    async deleteOfflineFirst(id, userId, options = {}) {
        const query = `DELETE FROM ${this.tableName} WHERE id = $1`;
        try {
            const result = await offline_first_data_service_1.offlineFirstDataService.mutateData('DELETE', this.tableName, query, [id], id, userId);
            return result.rowCount > 0;
        }
        catch (error) {
            console.error(`Offline-first delete failed for ${this.tableName}:`, error);
            throw error;
        }
    }
    /**
     * Enhanced soft delete method with offline-first strategy
     */
    async softDeleteOfflineFirst(id, userId, options = {}) {
        const query = `
      UPDATE ${this.tableName} 
      SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = $1 
      RETURNING *
    `;
        // Generate optimistic data
        const optimisticData = options.enableOptimisticUpdates !== false ? {
            id,
            is_deleted: true,
            updated_at: new Date()
        } : undefined;
        try {
            const result = await offline_first_data_service_1.offlineFirstDataService.mutateData('UPDATE', this.tableName, query, [id], id, userId, optimisticData);
            if (result.rows.length === 0) {
                throw new types_1.DatabaseError(`Record not found in ${this.tableName}`, 'NOT_FOUND');
            }
            return result.rows[0];
        }
        catch (error) {
            console.error(`Offline-first soft delete failed for ${this.tableName}:`, error);
            throw error;
        }
    }
    /**
     * Find by ID with offline-first strategy
     */
    async findByIdOfflineFirst(id, options = {}) {
        const query = `SELECT * FROM ${this.tableName} WHERE id = $1`;
        try {
            const result = await this.findOfflineFirst(query, [id], options);
            return result.rows[0] || null;
        }
        catch (error) {
            console.error(`Offline-first findById failed for ${this.tableName}:`, error);
            return null;
        }
    }
    /**
     * Find all with offline-first strategy and pagination
     */
    async findAllOfflineFirst(pagination, filters, options = {}) {
        let query = `SELECT * FROM ${this.tableName} WHERE 1=1`;
        const params = [];
        let paramIndex = 1;
        // Apply filters
        if (filters) {
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    query += ` AND ${key} = $${paramIndex}`;
                    params.push(value);
                    paramIndex++;
                }
            });
        }
        // Add ordering
        query += ` ORDER BY created_at DESC`;
        // Add pagination
        if (pagination) {
            const { page = 1, limit = 20 } = pagination;
            const offset = (page - 1) * limit;
            query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
            params.push(limit, offset);
        }
        try {
            const result = await this.findOfflineFirst(query, params, options);
            // Get total count for pagination
            let countQuery = `SELECT COUNT(*) as total FROM ${this.tableName} WHERE 1=1`;
            const countParams = [];
            let countParamIndex = 1;
            if (filters) {
                Object.entries(filters).forEach(([key, value]) => {
                    if (value !== undefined && value !== null) {
                        countQuery += ` AND ${key} = $${countParamIndex}`;
                        countParams.push(value);
                        countParamIndex++;
                    }
                });
            }
            const countResult = await this.findOfflineFirst(countQuery, countParams, options);
            const total = countResult.rows[0]?.total || 0;
            const hasMore = pagination ?
                (pagination.page * pagination.limit) < total :
                false;
            return {
                data: result.rows,
                total,
                hasMore
            };
        }
        catch (error) {
            console.error(`Offline-first findAll failed for ${this.tableName}:`, error);
            return { data: [], total: 0, hasMore: false };
        }
    }
    /**
     * Check if record exists with offline-first strategy
     */
    async existsOfflineFirst(id, options = {}) {
        const query = `SELECT EXISTS(SELECT 1 FROM ${this.tableName} WHERE id = $1) as exists`;
        try {
            const result = await this.findOfflineFirst(query, [id], options);
            return result.rows[0]?.exists || false;
        }
        catch (error) {
            console.error(`Offline-first exists check failed for ${this.tableName}:`, error);
            return false;
        }
    }
    /**
     * Get offline status for this DAO
     */
    getOfflineStatus() {
        return {
            tableName: this.tableName,
            ...offline_first_data_service_1.offlineFirstDataService.getOfflineStatus()
        };
    }
    /**
     * Force sync pending operations for this table
     */
    async forceSyncTable() {
        try {
            await offline_first_data_service_1.offlineFirstDataService.forceSyncPendingOperations();
        }
        catch (error) {
            console.error(`Failed to force sync ${this.tableName}:`, error);
            throw error;
        }
    }
    /**
     * Clear cache for this table
     */
    clearTableCache() {
        // This would ideally clear only cache entries for this table
        // For now, we clear all cache
        offline_first_data_service_1.offlineFirstDataService.clearCache();
    }
}
exports.OfflineFirstBaseDAO = OfflineFirstBaseDAO;
