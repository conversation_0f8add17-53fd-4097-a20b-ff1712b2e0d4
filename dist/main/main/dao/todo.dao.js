"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.todoDAO = exports.TodoDAO = void 0;
const offline_first_base_dao_1 = require("./offline-first-base.dao");
const types_1 = require("../../shared/types");
class TodoDAO extends offline_first_base_dao_1.OfflineFirstBaseDAO {
    constructor() {
        super('todos');
    }
    /**
     * Create a new todo with offline-first strategy
     */
    async createTodoOfflineFirst(todoData, userId, options = {}) {
        // Sanitize input data
        const sanitizedData = (0, types_1.sanitizeTodoData)({ ...todoData, user_id: userId });
        // Comprehensive validation
        const validationResult = (0, types_1.validateTodo)(sanitizedData, {
            isCreate: true,
            requireUserId: true,
            allowPastDueDates: true
        });
        if (!validationResult.isValid) {
            throw types_1.TodoErrorFactory.createValidationError(validationResult.errors, {
                userId,
                operation: 'create_todo'
            });
        }
        return this.createOfflineFirst(sanitizedData, userId, {
            enableOptimisticUpdates: true,
            priority: 'high',
            ...options
        });
    }
    /**
     * Create a new todo with proper validation and defaults (legacy method)
     */
    async createTodo(todoData, userId) {
        // Sanitize input data
        const sanitizedData = (0, types_1.sanitizeTodoData)({ ...todoData, user_id: userId });
        // Comprehensive validation
        const validationResult = (0, types_1.validateTodo)(sanitizedData, {
            isCreate: true,
            requireUserId: true,
            allowPastDueDates: true
        });
        if (!validationResult.isValid) {
            throw types_1.TodoErrorFactory.createValidationError(validationResult.errors, {
                userId,
                operation: 'create_todo'
            });
        }
        // Get next position for the category
        const position = await this.getNextPosition(sanitizedData.category_id || null, userId);
        // Prepare data with defaults using sanitized data
        const data = {
            ...sanitizedData,
            user_id: userId,
            status: sanitizedData.status || 'pending',
            priority: sanitizedData.priority || 'medium',
            tags: sanitizedData.tags || [],
            position,
            metadata: sanitizedData.metadata || {},
            is_deleted: false,
        };
        // Use custom query to handle arrays and JSON properly
        const fields = Object.keys(data).filter(key => data[key] !== undefined);
        const values = [];
        let paramIndex = 1;
        const placeholders = fields.map(key => {
            const value = data[key];
            // Handle JSON fields
            if (key === 'metadata' && typeof value === 'object') {
                values.push(JSON.stringify(value));
                return `$${paramIndex++}`;
            }
            // Handle array fields - use array literal syntax for DuckDB
            if (key === 'tags' && Array.isArray(value)) {
                if (value.length === 0) {
                    return `ARRAY[]::VARCHAR[]`;
                }
                else {
                    // Create array literal with quoted strings
                    const arrayLiteral = `ARRAY[${value.map(tag => `'${tag.replace(/'/g, "''")}'`).join(', ')}]`;
                    return arrayLiteral;
                }
            }
            // Regular fields
            values.push(value);
            return `$${paramIndex++}`;
        });
        const query = `
      INSERT INTO todos (${fields.join(', ')})
      VALUES (${placeholders.join(', ')})
      RETURNING *
    `;
        const result = await this.executeQuery(query, values);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError(`Failed to create todo`, 'CREATE_ERROR');
        }
        return result.rows[0];
    }
    /**
     * Find todo by ID with user authorization (offline-first)
     */
    async findByIdForUserOfflineFirst(id, userId, options = {}) {
        const result = await this.findOfflineFirst(`SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.id = $1 AND t.user_id = $2 AND t.is_deleted = FALSE`, [id, userId], options);
        return result.rows[0] || null;
    }
    /**
     * Find todo by ID with user authorization (legacy method)
     */
    async findByIdForUser(id, userId) {
        const result = await this.executeQuery(`SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.id = $1 AND t.user_id = $2 AND t.is_deleted = FALSE`, [id, userId]);
        return result.rows[0] || null;
    }
    /**
     * Update todo with user authorization and special handling
     */
    async updateTodo(id, data, userId) {
        // Validate user authorization first
        const existingTodo = await this.findByIdForUser(id, userId);
        if (!existingTodo) {
            throw types_1.TodoErrorFactory.createNotFoundError('Todo', {
                userId,
                todoId: id,
                operation: 'update_todo'
            });
        }
        // Sanitize input data
        const sanitizedData = (0, types_1.sanitizeTodoData)(data);
        // Prepare update data
        const updateData = { ...sanitizedData };
        delete updateData.id;
        delete updateData.user_id; // Prevent user_id changes
        delete updateData.created_at; // Prevent created_at changes
        // Validate status transition if status is being changed
        if (updateData.status && updateData.status !== existingTodo.status) {
            const transitionErrors = (0, types_1.validateStatusTransition)(existingTodo.status, updateData.status);
            if (transitionErrors.length > 0) {
                throw types_1.TodoErrorFactory.createValidationError(transitionErrors, {
                    userId,
                    todoId: id,
                    operation: 'update_todo_status'
                });
            }
        }
        // Comprehensive validation for update data
        const validationResult = (0, types_1.validateTodo)(updateData, {
            isCreate: false,
            requireUserId: false,
            allowPastDueDates: true
        });
        if (!validationResult.isValid) {
            throw types_1.TodoErrorFactory.createValidationError(validationResult.errors, {
                userId,
                todoId: id,
                operation: 'update_todo'
            });
        }
        // Handle status change logic
        if (updateData.status === 'completed' && existingTodo.status !== 'completed') {
            updateData.completed_at = new Date();
        }
        else if (updateData.status !== 'completed' && existingTodo.status === 'completed') {
            updateData.completed_at = undefined;
        }
        const fields = Object.keys(updateData).filter(key => updateData[key] !== undefined);
        if (fields.length === 0) {
            throw new types_1.DatabaseError('No fields to update', 'UPDATE_ERROR');
        }
        const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');
        const values = fields.map(key => updateData[key]);
        values.push(id, userId);
        const query = `
      UPDATE todos
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${values.length - 1} AND user_id = $${values.length} AND is_deleted = FALSE
      RETURNING *
    `;
        const result = await this.executeQuery(query, values);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    /**
     * Delete todo with user authorization
     */
    async deleteTodo(id, userId) {
        const result = await this.executeQuery(`DELETE FROM todos WHERE id = $1 AND user_id = $2`, [id, userId]);
        return result.rowCount > 0;
    }
    /**
     * Soft delete todo with user authorization
     */
    async softDeleteTodo(id, userId) {
        const result = await this.executeQuery(`UPDATE todos
       SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP
       WHERE id = $1 AND user_id = $2 AND is_deleted = FALSE
       RETURNING *`, [id, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    /**
     * Find todos by user ID with offline-first strategy
     */
    async findByUserIdOfflineFirst(userId, pagination, filters, options = {}) {
        const result = await this.findAllOfflineFirst(pagination, { ...filters, user_id: userId, is_deleted: false }, options);
        return {
            todos: result.data,
            total: result.total,
            hasMore: result.hasMore
        };
    }
    async findByUserId(userId, pagination, filters) {
        let whereClause = 'WHERE t.user_id = $1 AND t.is_deleted = FALSE';
        let params = [userId];
        let paramIndex = 2;
        // Build additional filters
        if (filters) {
            if (filters.status) {
                whereClause += ` AND t.status = $${paramIndex}`;
                params.push(filters.status);
                paramIndex++;
            }
            if (filters.priority) {
                whereClause += ` AND t.priority = $${paramIndex}`;
                params.push(filters.priority);
                paramIndex++;
            }
            if (filters.category_id) {
                whereClause += ` AND t.category_id = $${paramIndex}`;
                params.push(filters.category_id);
                paramIndex++;
            }
            if (filters.due_date_from) {
                whereClause += ` AND t.due_date >= $${paramIndex}`;
                params.push(filters.due_date_from);
                paramIndex++;
            }
            if (filters.due_date_to) {
                whereClause += ` AND t.due_date <= $${paramIndex}`;
                params.push(filters.due_date_to);
                paramIndex++;
            }
            if (filters.tags && filters.tags.length > 0) {
                whereClause += ` AND t.tags && $${paramIndex}`;
                params.push(filters.tags);
                paramIndex++;
            }
            if (filters.search) {
                whereClause += ` AND (t.title ILIKE $${paramIndex} OR t.description ILIKE $${paramIndex})`;
                params.push(`%${filters.search}%`);
                paramIndex++;
            }
            if (filters.is_completed !== undefined) {
                if (filters.is_completed) {
                    whereClause += ` AND t.status = 'completed'`;
                }
                else {
                    whereClause += ` AND t.status != 'completed'`;
                }
            }
            if (filters.is_overdue) {
                whereClause += ` AND t.due_date < CURRENT_TIMESTAMP AND t.status NOT IN ('completed', 'cancelled')`;
            }
        }
        // Get total count
        const countQuery = `SELECT COUNT(*) as total FROM todos t ${whereClause}`;
        const countResult = await this.executeQuery(countQuery, params);
        const total = countResult.rows[0]?.total || 0;
        // Build main query
        let query = `
      SELECT t.*, c.name as category_name, c.color as category_color
      FROM todos t
      LEFT JOIN categories c ON t.category_id = c.id
      ${whereClause}
    `;
        // Add ordering
        if (pagination?.sortBy) {
            const sortOrder = pagination.sortOrder || 'ASC';
            query += ` ORDER BY ${pagination.sortBy} ${sortOrder}`;
        }
        else {
            // Default ordering: by position, then by created_at
            query += ` ORDER BY t.position ASC, t.created_at DESC`;
        }
        // Add pagination
        if (pagination?.limit) {
            query += ` LIMIT $${paramIndex}`;
            params.push(pagination.limit);
            paramIndex++;
            if (pagination.page && pagination.page > 1) {
                const offset = (pagination.page - 1) * pagination.limit;
                query += ` OFFSET $${paramIndex}`;
                params.push(offset);
            }
        }
        const result = await this.executeQuery(query, params);
        return {
            data: result.rows,
            total
        };
    }
    /**
     * Update todo status with offline-first strategy
     */
    async updateStatusOfflineFirst(todoId, status, userId, options = {}) {
        const updates = { status, updated_at: new Date() };
        // Set completed_at when status is completed
        if (status === 'completed') {
            updates.completed_at = new Date();
        }
        else {
            updates.completed_at = null;
        }
        return this.updateOfflineFirst(todoId, updates, userId, {
            enableOptimisticUpdates: true,
            priority: 'high',
            ...options
        });
    }
    async updateStatus(todoId, status, userId) {
        const updates = { status, updated_at: new Date() };
        // Set completed_at when status is completed
        if (status === 'completed') {
            updates.completed_at = new Date();
        }
        else {
            updates.completed_at = null;
        }
        const result = await this.executeQuery(`UPDATE todos
       SET status = $1, completed_at = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3 AND user_id = $4 AND is_deleted = FALSE
       RETURNING *`, [status, updates.completed_at, todoId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    /**
     * Soft delete todo with offline-first strategy
     */
    async softDeleteTodoOfflineFirst(todoId, userId, options = {}) {
        return this.softDeleteOfflineFirst(todoId, userId, {
            enableOptimisticUpdates: true,
            priority: 'high',
            ...options
        });
    }
    async updatePriority(todoId, priority, userId) {
        const result = await this.executeQuery(`UPDATE todos
       SET priority = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`, [priority, todoId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async updatePosition(todoId, newPosition, userId) {
        return await this.executeTransaction(async (execute) => {
            // Get the current todo
            const currentResult = await execute('SELECT position, category_id FROM todos WHERE id = $1 AND user_id = $2 AND is_deleted = FALSE', [todoId, userId]);
            if (currentResult.rows.length === 0) {
                throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
            }
            const currentTodo = currentResult.rows[0];
            const oldPosition = currentTodo.position;
            const categoryId = currentTodo.category_id;
            // Update positions of other todos in the same category
            if (newPosition > oldPosition) {
                // Moving down: shift todos up
                await execute(`UPDATE todos 
           SET position = position - 1, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1 AND category_id = $2 AND position > $3 AND position <= $4 AND is_deleted = FALSE`, [userId, categoryId, oldPosition, newPosition]);
            }
            else if (newPosition < oldPosition) {
                // Moving up: shift todos down
                await execute(`UPDATE todos 
           SET position = position + 1, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1 AND category_id = $2 AND position >= $3 AND position < $4 AND is_deleted = FALSE`, [userId, categoryId, newPosition, oldPosition]);
            }
            // Update the target todo's position
            const result = await execute(`UPDATE todos 
         SET position = $1, updated_at = CURRENT_TIMESTAMP
         WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
         RETURNING *`, [newPosition, todoId, userId]);
            return result.rows[0];
        });
    }
    async addTags(todoId, tags, userId) {
        const result = await this.executeQuery(`UPDATE todos 
       SET tags = array_cat(tags, $1), updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`, [tags, todoId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async removeTags(todoId, tags, userId) {
        const result = await this.executeQuery(`UPDATE todos 
       SET tags = array_remove_all(tags, $1), updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`, [tags, todoId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async getOverdueTodos(userId) {
        const result = await this.executeQuery(`SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.user_id = $1 
         AND t.due_date < CURRENT_TIMESTAMP 
         AND t.status NOT IN ('completed', 'cancelled')
         AND t.is_deleted = FALSE
       ORDER BY t.due_date ASC`, [userId]);
        return result.rows;
    }
    async getTodosWithReminders(beforeTime) {
        const time = beforeTime || new Date();
        const result = await this.executeQuery(`SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.reminder_at <= $1 
         AND t.status NOT IN ('completed', 'cancelled')
         AND t.is_deleted = FALSE
       ORDER BY t.reminder_at ASC`, [time]);
        return result.rows;
    }
    async getTodosByCategory(categoryId, userId) {
        const result = await this.executeQuery(`SELECT * FROM todos 
       WHERE category_id = $1 AND user_id = $2 AND is_deleted = FALSE
       ORDER BY position ASC, created_at DESC`, [categoryId, userId]);
        return result.rows;
    }
    async searchTodos(userId, searchTerm, limit = 20) {
        const result = await this.executeQuery(`SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.user_id = $1 
         AND t.is_deleted = FALSE
         AND (
           t.title ILIKE $2 
           OR t.description ILIKE $2 
           OR $3 = ANY(t.tags)
         )
       ORDER BY 
         CASE WHEN t.title ILIKE $2 THEN 1 ELSE 2 END,
         t.updated_at DESC
       LIMIT $4`, [userId, `%${searchTerm}%`, searchTerm, limit]);
        return result.rows;
    }
    async getUserTodoStats(userId) {
        const result = await this.executeQuery(`SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN due_date < CURRENT_TIMESTAMP AND status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue
       FROM todos 
       WHERE user_id = $1 AND is_deleted = FALSE`, [userId]);
        const stats = result.rows[0] || {
            total: 0,
            completed: 0,
            pending: 0,
            in_progress: 0,
            overdue: 0
        };
        const completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;
        return {
            total: stats.total,
            completed: stats.completed,
            pending: stats.pending,
            inProgress: stats.in_progress,
            overdue: stats.overdue,
            completionRate: Math.round(completionRate * 100) / 100,
        };
    }
    async getNextPosition(categoryId, userId) {
        const result = await this.executeQuery(`SELECT COALESCE(MAX(position), 0) + 1 as max_position
       FROM todos 
       WHERE user_id = $1 AND category_id = $2 AND is_deleted = FALSE`, [userId, categoryId]);
        return result.rows[0]?.max_position || 1;
    }
    async moveToCategory(todoId, newCategoryId, userId) {
        return await this.executeTransaction(async (execute) => {
            // Get next position in the new category
            const nextPosition = await this.getNextPosition(newCategoryId, userId);
            // Update the todo
            const result = await execute(`UPDATE todos
         SET category_id = $1, position = $2, updated_at = CURRENT_TIMESTAMP
         WHERE id = $3 AND user_id = $4 AND is_deleted = FALSE
         RETURNING *`, [newCategoryId, nextPosition, todoId, userId]);
            if (result.rows.length === 0) {
                throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
            }
            return result.rows[0];
        });
    }
    // Tag Management Methods
    async getAllTags(userId) {
        const result = await this.executeQuery(`SELECT
         tag,
         COUNT(*) as count,
         COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count
       FROM (
         SELECT unnest(tags) as tag, status
         FROM todos
         WHERE user_id = $1 AND is_deleted = FALSE
       ) tag_data
       GROUP BY tag
       ORDER BY count DESC, tag ASC`, [userId]);
        return result.rows;
    }
    async getTagSuggestions(userId, query, limit = 10) {
        const result = await this.executeQuery(`SELECT DISTINCT tag
       FROM (
         SELECT unnest(tags) as tag
         FROM todos
         WHERE user_id = $1 AND is_deleted = FALSE
       ) tag_data
       WHERE tag ILIKE $2
       ORDER BY tag ASC
       LIMIT $3`, [userId, `%${query}%`, limit]);
        return result.rows.map(row => row.tag);
    }
    async getPopularTags(userId, limit = 20) {
        const result = await this.executeQuery(`SELECT tag
       FROM (
         SELECT unnest(tags) as tag
         FROM todos
         WHERE user_id = $1 AND is_deleted = FALSE
       ) tag_data
       GROUP BY tag
       ORDER BY COUNT(*) DESC, tag ASC
       LIMIT $2`, [userId, limit]);
        return result.rows.map(row => row.tag);
    }
    async getRecentTags(userId, limit = 10) {
        const result = await this.executeQuery(`SELECT DISTINCT tag
       FROM (
         SELECT unnest(tags) as tag, updated_at
         FROM todos
         WHERE user_id = $1 AND is_deleted = FALSE
         ORDER BY updated_at DESC
         LIMIT 50
       ) recent_todos
       GROUP BY tag
       ORDER BY MAX(updated_at) DESC
       LIMIT $2`, [userId, limit]);
        return result.rows.map(row => row.tag);
    }
    async getTagStats(userId, tag) {
        const result = await this.executeQuery(`SELECT
         COUNT(*) as total_count,
         COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
         COUNT(CASE WHEN status != 'completed' THEN 1 END) as pending_count,
         MAX(updated_at) as last_used
       FROM todos
       WHERE user_id = $1 AND is_deleted = FALSE AND $2 = ANY(tags)`, [userId, tag]);
        return result.rows.length > 0 ? result.rows[0] : null;
    }
}
exports.TodoDAO = TodoDAO;
exports.todoDAO = new TodoDAO();
