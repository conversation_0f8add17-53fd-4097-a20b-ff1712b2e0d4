import { ConflictResolver } from './conflict-resolver.service';
import { NetworkDetector } from './network-detector.service';
export interface SyncConfiguration {
    syncInterval: number;
    batchSize: number;
    maxRetries: number;
    conflictResolutionStrategy: 'last-write-wins' | 'manual' | 'merge';
    enableRealTime: boolean;
    compressionEnabled: boolean;
    deltaSync: boolean;
}
export interface SyncOperation {
    id: string;
    operation: 'CREATE' | 'UPDATE' | 'DELETE';
    table: string;
    recordId: string;
    data: any;
    localTimestamp: Date;
    vectorClock: VectorClock;
    userId: string;
    dependencies: string[];
    priority: 'high' | 'medium' | 'low';
    retryCount: number;
    status: 'pending' | 'syncing' | 'synced' | 'failed' | 'conflict';
}
export interface VectorClock {
    [deviceId: string]: number;
}
export interface ConflictInfo {
    operation: SyncOperation | null;
    remoteData: any;
    reason: string;
    localData?: any;
    conflictType?: string;
    recommendations?: string[];
    tableName?: string;
    recordId?: string;
}
export interface SyncResult {
    success: boolean;
    processedOperations: number;
    conflicts: ConflictInfo[];
    errors: string[];
}
export interface SyncQueueStatus {
    totalOperations: number;
    pendingOperations: number;
    failedOperations: number;
    conflictOperations: number;
    isOnline: boolean;
    isSyncing: boolean;
}
export declare class SyncEngine {
    private config;
    private conflictResolver;
    private networkDetector;
    private syncTimer;
    private operationQueue;
    private syncInProgress;
    private deviceId;
    private vectorClock;
    private eventEmitter;
    constructor(config: SyncConfiguration, conflictResolver: ConflictResolver, networkDetector: NetworkDetector);
    private generateDeviceId;
    private initializeVectorClock;
    private persistVectorClock;
    private setupSyncScheduler;
    private setupNetworkListeners;
    queueOperation(operation: Omit<SyncOperation, 'id' | 'vectorClock' | 'retryCount' | 'status'>): Promise<void>;
    private persistOperationQueue;
    private loadOperationQueue;
    startSync(): Promise<SyncResult>;
    private pushChangesToCloud;
    private createBatches;
    private processBatch;
    private pushOperationToCloud;
    private pullChangesFromCloud;
    private getLastSyncTimestamp;
    private updateLastSyncTimestamp;
    private getRemoteChanges;
    private detectConflict;
    private applyRemoteChange;
    private updateSyncMetadata;
    private resolveConflicts;
    private createDetailedConflictInfo;
    private storeConflictForManualResolution;
    private applyConflictResolution;
    private scheduleSyncAttempt;
    getQueueStatus(): Promise<SyncQueueStatus>;
    clearQueue(): Promise<void>;
    on(event: string, listener: (...args: any[]) => void): void;
    off(event: string, listener: (...args: any[]) => void): void;
    stop(): Promise<void>;
}
