"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncEngine = void 0;
const events_1 = require("events");
const connection_1 = require("../database/connection");
const service_1 = require("../mcp/service");
class SyncEngine {
    config;
    conflictResolver;
    networkDetector;
    syncTimer = null;
    operationQueue = [];
    syncInProgress = false;
    deviceId;
    vectorClock = {};
    eventEmitter;
    constructor(config, conflictResolver, networkDetector) {
        this.config = config;
        this.conflictResolver = conflictResolver;
        this.networkDetector = networkDetector;
        this.eventEmitter = new events_1.EventEmitter();
        this.deviceId = this.generateDeviceId();
        this.initializeVectorClock();
        this.setupSyncScheduler();
        this.setupNetworkListeners();
    }
    generateDeviceId() {
        // Generate a unique device ID based on machine characteristics
        const crypto = require('crypto');
        const os = require('os');
        const machineId = `${os.hostname()}-${os.platform()}-${os.arch()}`;
        return crypto.createHash('sha256').update(machineId).digest('hex').substring(0, 16);
    }
    async initializeVectorClock() {
        try {
            // Load vector clock from database
            const result = await connection_1.dbConnection.executeQuery('SELECT vector_clock FROM sync_state WHERE device_id = $1', [this.deviceId]);
            if (result.rows.length > 0) {
                this.vectorClock = JSON.parse(result.rows[0].vector_clock);
            }
            else {
                // Initialize new vector clock
                this.vectorClock[this.deviceId] = 0;
                await this.persistVectorClock();
            }
        }
        catch (error) {
            console.warn('Failed to load vector clock, using default:', error);
            this.vectorClock[this.deviceId] = 0;
        }
    }
    async persistVectorClock() {
        try {
            await connection_1.dbConnection.executeQuery(`INSERT INTO sync_state (device_id, vector_clock, updated_at)
         VALUES ($1, $2, $3)
         ON CONFLICT (device_id)
         DO UPDATE SET vector_clock = $2, updated_at = $3`, [this.deviceId, JSON.stringify(this.vectorClock), new Date().toISOString()]);
        }
        catch (error) {
            console.error('Failed to persist vector clock:', error);
        }
    }
    setupSyncScheduler() {
        if (this.config.syncInterval > 0) {
            this.syncTimer = setInterval(async () => {
                if (this.networkDetector.isOnline() && !this.syncInProgress) {
                    await this.startSync();
                }
            }, this.config.syncInterval);
        }
    }
    setupNetworkListeners() {
        this.networkDetector.on('online', () => {
            console.log('Network came online, triggering sync');
            this.scheduleSyncAttempt();
        });
        this.networkDetector.on('offline', () => {
            console.log('Network went offline, pausing sync');
        });
    }
    async queueOperation(operation) {
        // Increment vector clock for this device
        this.vectorClock[this.deviceId] = (this.vectorClock[this.deviceId] || 0) + 1;
        const syncOperation = {
            ...operation,
            id: crypto.randomUUID(),
            vectorClock: { ...this.vectorClock },
            retryCount: 0,
            status: 'pending',
        };
        // Add to queue
        this.operationQueue.push(syncOperation);
        // Persist queue to database
        await this.persistOperationQueue();
        // Persist vector clock
        await this.persistVectorClock();
        // Emit event for UI updates
        this.eventEmitter.emit('sync:operationQueued', syncOperation);
        // Trigger sync if online
        if (this.networkDetector.isOnline()) {
            this.scheduleSyncAttempt();
        }
    }
    async persistOperationQueue() {
        try {
            // Clear existing queue for this device
            await connection_1.dbConnection.executeQuery('DELETE FROM sync_queue WHERE device_id = $1', [this.deviceId]);
            // Insert current queue
            for (const operation of this.operationQueue) {
                await connection_1.dbConnection.executeQuery(`INSERT INTO sync_queue (
            device_id, operation_id, operation_type, table_name, record_id, 
            data, vector_clock, user_id, priority, retry_count, status, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)`, [
                    this.deviceId,
                    operation.id,
                    operation.operation,
                    operation.table,
                    operation.recordId,
                    JSON.stringify(operation.data),
                    JSON.stringify(operation.vectorClock),
                    operation.userId,
                    operation.priority,
                    operation.retryCount,
                    operation.status
                ]);
            }
        }
        catch (error) {
            console.error('Failed to persist operation queue:', error);
        }
    }
    async loadOperationQueue() {
        try {
            const result = await connection_1.dbConnection.executeQuery('SELECT * FROM sync_queue WHERE device_id = $1 ORDER BY created_at ASC', [this.deviceId]);
            this.operationQueue = result.rows.map(row => ({
                id: row.operation_id,
                operation: row.operation_type,
                table: row.table_name,
                recordId: row.record_id,
                data: JSON.parse(row.data),
                localTimestamp: new Date(row.created_at),
                vectorClock: JSON.parse(row.vector_clock),
                userId: row.user_id,
                dependencies: [], // TODO: Implement dependencies
                priority: row.priority,
                retryCount: row.retry_count,
                status: row.status
            }));
        }
        catch (error) {
            console.warn('Failed to load operation queue:', error);
            this.operationQueue = [];
        }
    }
    async startSync() {
        if (this.syncInProgress) {
            throw new Error('Sync already in progress');
        }
        if (!this.networkDetector.isOnline()) {
            throw new Error('Cannot sync while offline');
        }
        this.syncInProgress = true;
        this.eventEmitter.emit('sync:started');
        try {
            // Load operation queue from database
            await this.loadOperationQueue();
            // Push local changes to cloud
            const pushResult = await this.pushChangesToCloud();
            // Pull remote changes from cloud
            const pullResult = await this.pullChangesFromCloud();
            // Combine results
            const result = {
                success: pushResult.success && pullResult.success,
                processedOperations: pushResult.processedOperations + pullResult.processedOperations,
                conflicts: [...pushResult.conflicts, ...pullResult.conflicts],
                errors: [...pushResult.errors, ...pullResult.errors]
            };
            // Resolve conflicts if any
            if (result.conflicts.length > 0) {
                const conflictResult = await this.resolveConflicts(result.conflicts);
                result.errors.push(...conflictResult.errors);
            }
            this.eventEmitter.emit('sync:completed', result);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown sync error';
            this.eventEmitter.emit('sync:error', error);
            return {
                success: false,
                processedOperations: 0,
                conflicts: [],
                errors: [errorMessage]
            };
        }
        finally {
            this.syncInProgress = false;
        }
    }
    async pushChangesToCloud() {
        const result = {
            success: true,
            processedOperations: 0,
            conflicts: [],
            errors: []
        };
        const pendingOperations = this.operationQueue.filter(op => op.status === 'pending');
        const batches = this.createBatches(pendingOperations, this.config.batchSize);
        for (const batch of batches) {
            try {
                await this.processBatch(batch);
                result.processedOperations += batch.length;
            }
            catch (error) {
                result.success = false;
                result.errors.push(`Batch processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        return result;
    }
    createBatches(items, batchSize) {
        const batches = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }
    async processBatch(operations) {
        for (const operation of operations) {
            try {
                operation.status = 'syncing';
                await this.pushOperationToCloud(operation);
                operation.status = 'synced';
                // Update sync metadata
                await this.updateSyncMetadata(operation.table, operation.recordId, 'synced');
            }
            catch (error) {
                operation.retryCount++;
                if (operation.retryCount >= this.config.maxRetries) {
                    operation.status = 'failed';
                }
                else {
                    operation.status = 'pending';
                }
                throw error;
            }
        }
        // Remove synced operations from queue
        this.operationQueue = this.operationQueue.filter(op => op.status !== 'synced');
        await this.persistOperationQueue();
    }
    async pushOperationToCloud(operation) {
        try {
            switch (operation.operation) {
                case 'CREATE':
                    await service_1.mcpService.insertData(operation.table, [operation.data]);
                    break;
                case 'UPDATE':
                    await service_1.mcpService.updateData(operation.table, operation.data, { id: operation.recordId });
                    break;
                case 'DELETE':
                    await service_1.mcpService.deleteData(operation.table, { id: operation.recordId });
                    break;
            }
        }
        catch (error) {
            console.error(`Failed to push ${operation.operation} operation for ${operation.table}:`, error);
            throw error;
        }
    }
    async pullChangesFromCloud() {
        const result = {
            success: true,
            processedOperations: 0,
            conflicts: [],
            errors: []
        };
        try {
            // Get last sync timestamp
            const lastSyncTimestamp = await this.getLastSyncTimestamp();
            // Pull changes for each table
            const tables = ['todos', 'categories', 'user_profiles'];
            for (const table of tables) {
                try {
                    const remoteChanges = await this.getRemoteChanges(table, lastSyncTimestamp);
                    for (const change of remoteChanges) {
                        const conflictDetected = await this.detectConflict(change, table);
                        if (conflictDetected) {
                            const conflictInfo = await this.createDetailedConflictInfo(table, change);
                            result.conflicts.push(conflictInfo);
                            // Store conflict in database for UI resolution
                            await this.storeConflictForManualResolution(conflictInfo);
                        }
                        else {
                            await this.applyRemoteChange(table, change);
                            result.processedOperations++;
                        }
                    }
                }
                catch (error) {
                    result.errors.push(`Failed to pull changes for ${table}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
            // Update last sync timestamp
            await this.updateLastSyncTimestamp(new Date());
        }
        catch (error) {
            result.success = false;
            result.errors.push(`Pull changes failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        return result;
    }
    async getLastSyncTimestamp() {
        try {
            const result = await connection_1.dbConnection.executeQuery('SELECT last_sync_at FROM sync_state WHERE device_id = $1', [this.deviceId]);
            return result.rows[0]?.last_sync_at || '1970-01-01T00:00:00Z';
        }
        catch (error) {
            console.warn('Failed to get last sync timestamp:', error);
            return '1970-01-01T00:00:00Z';
        }
    }
    async updateLastSyncTimestamp(timestamp) {
        try {
            await connection_1.dbConnection.executeQuery(`INSERT INTO sync_state (device_id, last_sync_at, updated_at)
         VALUES ($1, $2, $3)
         ON CONFLICT (device_id)
         DO UPDATE SET last_sync_at = $2, updated_at = $3`, [this.deviceId, timestamp.toISOString(), new Date().toISOString()]);
        }
        catch (error) {
            console.error('Failed to update last sync timestamp:', error);
        }
    }
    async getRemoteChanges(table, since) {
        try {
            const query = `
        SELECT * FROM ${table} 
        WHERE updated_at > ? 
        ORDER BY updated_at ASC
      `;
            const result = await service_1.mcpService.executeQuery(query, [since]);
            return result.rows || [];
        }
        catch (error) {
            console.error(`Failed to get remote changes for ${table}:`, error);
            return [];
        }
    }
    async detectConflict(remoteData, table) {
        try {
            // Check if there are local changes for this record
            const result = await connection_1.dbConnection.executeQuery('SELECT sync_status, local_version, remote_version FROM sync_metadata WHERE table_name = $1 AND record_id = $2', [table, remoteData.id]);
            if (result.rows.length === 0) {
                return false; // No local metadata, no conflict
            }
            const metadata = result.rows[0];
            // Check for pending local changes
            if (metadata.sync_status === 'pending') {
                return true;
            }
            // Check version conflicts
            if (remoteData.version && metadata.remote_version && remoteData.version <= metadata.remote_version) {
                return false; // Remote data is not newer
            }
            // Check for concurrent modifications using vector clocks if available
            if (remoteData.vector_clock && metadata.local_version) {
                // This would require more sophisticated vector clock comparison
                // For now, assume conflict if both have been modified
                return true;
            }
            return false;
        }
        catch (error) {
            console.warn('Failed to detect conflict:', error);
            return false;
        }
    }
    async applyRemoteChange(table, change) {
        try {
            // Apply the change to local database
            await connection_1.dbConnection.executeQuery(`INSERT INTO ${table} (${Object.keys(change).join(', ')}) 
         VALUES (${Object.keys(change).map((_, i) => `$${i + 1}`).join(', ')})
         ON CONFLICT (id) DO UPDATE SET 
         ${Object.keys(change).filter(k => k !== 'id').map((k, i) => `${k} = $${i + 2}`).join(', ')}`, Object.values(change));
            // Update sync metadata
            await this.updateSyncMetadata(table, change.id, 'synced');
        }
        catch (error) {
            console.error(`Failed to apply remote change for ${table}:`, error);
            throw error;
        }
    }
    async updateSyncMetadata(table, recordId, status) {
        try {
            await connection_1.dbConnection.executeQuery(`INSERT INTO sync_metadata (table_name, record_id, sync_status, last_synced_at, updated_at)
         VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         ON CONFLICT (table_name, record_id)
         DO UPDATE SET sync_status = $3, last_synced_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP`, [table, recordId, status]);
        }
        catch (error) {
            console.error('Failed to update sync metadata:', error);
        }
    }
    async resolveConflicts(conflicts) {
        const errors = [];
        for (const conflict of conflicts) {
            try {
                const resolution = await this.conflictResolver.resolve(conflict);
                if (resolution.strategy === 'manual') {
                    // Store conflict for manual resolution
                    await this.storeConflictForManualResolution(conflict);
                    this.eventEmitter.emit('sync:conflictDetected', conflict);
                }
                else {
                    // Apply automatic resolution
                    await this.applyConflictResolution(resolution);
                }
            }
            catch (error) {
                errors.push(`Failed to resolve conflict: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        return { errors };
    }
    async createDetailedConflictInfo(table, remoteData) {
        try {
            // Get local data for comparison
            const localResult = await connection_1.dbConnection.executeQuery(`SELECT * FROM ${table} WHERE id = $1`, [remoteData.id]);
            const localData = localResult.rows[0] || null;
            // Get conflict resolver recommendations
            const conflictType = this.conflictResolver.detectConflictType({
                operation: null,
                remoteData,
                reason: 'Remote change conflicts with local data'
            });
            const recommendations = this.conflictResolver.getResolutionRecommendations({
                operation: null,
                remoteData,
                reason: 'Remote change conflicts with local data'
            });
            return {
                operation: null,
                remoteData,
                reason: 'Local changes conflict with remote',
                localData,
                conflictType,
                recommendations,
                tableName: table,
                recordId: remoteData.id
            };
        }
        catch (error) {
            console.error('Failed to create detailed conflict info:', error);
            return {
                operation: null,
                remoteData,
                reason: 'Local changes conflict with remote'
            };
        }
    }
    async storeConflictForManualResolution(conflict) {
        try {
            const table = conflict.tableName || conflict.operation?.table || conflict.remoteData.table;
            const recordId = conflict.recordId || conflict.operation?.recordId || conflict.remoteData.id;
            await connection_1.dbConnection.executeQuery(`INSERT INTO sync_metadata (table_name, record_id, sync_status, conflict_data, created_at, updated_at)
         VALUES ($1, $2, 'conflict', $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         ON CONFLICT (table_name, record_id)
         DO UPDATE SET sync_status = 'conflict', conflict_data = $3, updated_at = CURRENT_TIMESTAMP`, [table, recordId, JSON.stringify(conflict)]);
        }
        catch (error) {
            console.error('Failed to store conflict:', error);
        }
    }
    async applyConflictResolution(resolution) {
        // Implementation depends on resolution strategy
        console.log('Applying conflict resolution:', resolution);
    }
    scheduleSyncAttempt() {
        // Debounce sync attempts
        setTimeout(() => {
            if (this.networkDetector.isOnline() && !this.syncInProgress) {
                this.startSync().catch(console.error);
            }
        }, 1000);
    }
    async getQueueStatus() {
        await this.loadOperationQueue();
        return {
            totalOperations: this.operationQueue.length,
            pendingOperations: this.operationQueue.filter(op => op.status === 'pending').length,
            failedOperations: this.operationQueue.filter(op => op.status === 'failed').length,
            conflictOperations: this.operationQueue.filter(op => op.status === 'conflict').length,
            isOnline: this.networkDetector.isOnline(),
            isSyncing: this.syncInProgress,
        };
    }
    async clearQueue() {
        this.operationQueue = [];
        await this.persistOperationQueue();
    }
    on(event, listener) {
        this.eventEmitter.on(event, listener);
    }
    off(event, listener) {
        this.eventEmitter.off(event, listener);
    }
    async stop() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
        }
        this.eventEmitter.removeAllListeners();
    }
}
exports.SyncEngine = SyncEngine;
