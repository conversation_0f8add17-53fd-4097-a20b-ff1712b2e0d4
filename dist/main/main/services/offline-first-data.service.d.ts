import { EventEmitter } from 'events';
import { QueryResult } from '../../shared/types';
export interface OfflineFirstConfig {
    enableLocalCache: boolean;
    cacheExpirationTime: number;
    maxCacheSize: number;
    enableOptimisticUpdates: boolean;
    syncOnReconnect: boolean;
    retryFailedOperations: boolean;
}
export interface CachedData {
    data: any;
    timestamp: Date;
    expiresAt: Date;
    source: 'local' | 'remote';
    isStale: boolean;
}
export interface OfflineOperation {
    id: string;
    operation: 'CREATE' | 'UPDATE' | 'DELETE';
    table: string;
    recordId: string;
    data: any;
    userId: string;
    timestamp: Date;
    retryCount: number;
    status: 'pending' | 'syncing' | 'completed' | 'failed';
    priority: 'high' | 'medium' | 'low';
}
/**
 * Offline-First Data Service
 *
 * This service implements an offline-first data strategy where:
 * 1. All data operations work locally first
 * 2. Local DuckDB is the primary source of truth
 * 3. Cloud sync is secondary and happens in background
 * 4. Application works fully offline
 * 5. Optimistic updates provide immediate feedback
 */
export declare class OfflineFirstDataService extends EventEmitter {
    private config;
    private networkDetector;
    private isInitialized;
    private operationQueue;
    private dataCache;
    private readonly defaultConfig;
    constructor(config?: OfflineFirstConfig);
    initialize(): Promise<void>;
    /**
     * Primary data access method - always tries local first
     */
    getData<T>(table: string, query: string, params?: any[], options?: {
        useCache?: boolean;
        forceRefresh?: boolean;
    }): Promise<QueryResult<T>>;
    /**
     * Offline-first data mutation with optimistic updates
     */
    mutateData<T>(operation: 'CREATE' | 'UPDATE' | 'DELETE', table: string, query: string, params: any[] | undefined, recordId: string, userId: string, optimisticData?: T): Promise<QueryResult<T>>;
    /**
     * Check if application can work offline
     */
    isOfflineCapable(): boolean;
    /**
     * Get offline status and capabilities
     */
    getOfflineStatus(): {
        isOfflineCapable: boolean;
        isOnline: boolean;
        pendingOperations: number;
        lastSyncAt: Date | null;
        localDatabaseStatus: import("../../shared/types").ConnectionStatus;
        cacheSize: number;
        networkQuality: "excellent" | "good" | "poor" | "offline";
    };
    /**
     * Force sync all pending operations
     */
    forceSyncPendingOperations(): Promise<void>;
    /**
     * Clear local cache
     */
    clearCache(): void;
    /**
     * Get cache statistics
     */
    getCacheStats(): {
        totalEntries: number;
        staleEntries: number;
        localEntries: number;
        remoteEntries: number;
        estimatedSize: number;
    };
    private ensureLocalDatabaseReady;
    private loadPendingOperations;
    private queueOperation;
    private persistOperationQueue;
    private setupNetworkListeners;
    private startBackgroundSync;
    private triggerBackgroundSync;
    private syncOperation;
    private generateCacheKey;
    private getFromCache;
    private setCache;
    private cleanupCache;
    private estimateCacheSize;
    destroy(): Promise<void>;
}
export declare const offlineFirstDataService: OfflineFirstDataService;
