import { EventEmitter } from 'events';
import { SyncResult, SyncQueueStatus } from './sync-engine.service';
export interface SyncStatus {
    status: 'idle' | 'syncing' | 'error' | 'offline';
    lastSyncAt: Date | null;
    pendingChanges: number;
    errorMessage?: string;
    syncProgress?: {
        current: number;
        total: number;
        operation: string;
    };
}
export interface SyncManagerConfiguration {
    autoSyncEnabled: boolean;
    syncInterval: number;
    conflictResolutionStrategy: 'last-write-wins' | 'manual' | 'merge';
    batchSize: number;
    maxRetries: number;
    enableRealTimeSync: boolean;
}
export declare class SyncManager extends EventEmitter {
    private config;
    private syncEngine;
    private conflictResolver;
    private networkDetector;
    private syncStatus;
    private autoSyncTimer;
    private isInitialized;
    private readonly defaultConfig;
    constructor(config?: SyncManagerConfiguration);
    initialize(): Promise<void>;
    private ensureSyncTables;
    private setupEventListeners;
    startAutoSync(): Promise<void>;
    stopAutoSync(): void;
    performSync(): Promise<SyncResult>;
    queueOperation(operation: 'CREATE' | 'UPDATE' | 'DELETE', table: string, recordId: string, data: any, userId: string, priority?: 'high' | 'medium' | 'low'): Promise<void>;
    getSyncStatus(): SyncStatus;
    getQueueStatus(): Promise<SyncQueueStatus>;
    isOnline(): boolean;
    getNetworkStatus(): import("./network-detector.service").NetworkStatus;
    private updateSyncStatus;
    private updatePendingChangesCount;
    private refreshSyncStatus;
    forcePushChanges(): Promise<SyncResult>;
    forcePullChanges(): Promise<SyncResult>;
    clearQueue(): Promise<void>;
    getConflicts(): Promise<any[]>;
    resolveConflict(conflictId: string, resolution: 'local' | 'remote' | 'merge', mergedData?: any): Promise<void>;
    destroy(): Promise<void>;
}
export declare const syncManager: SyncManager;
