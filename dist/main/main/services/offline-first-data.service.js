"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.offlineFirstDataService = exports.OfflineFirstDataService = void 0;
const events_1 = require("events");
const connection_1 = require("../database/connection");
const sync_manager_service_1 = require("./sync-manager.service");
const network_detector_service_1 = require("./network-detector.service");
const types_1 = require("../../shared/types");
/**
 * Offline-First Data Service
 *
 * This service implements an offline-first data strategy where:
 * 1. All data operations work locally first
 * 2. Local DuckDB is the primary source of truth
 * 3. Cloud sync is secondary and happens in background
 * 4. Application works fully offline
 * 5. Optimistic updates provide immediate feedback
 */
class OfflineFirstDataService extends events_1.EventEmitter {
    config;
    networkDetector;
    isInitialized = false;
    operationQueue = [];
    dataCache = new Map();
    defaultConfig = {
        enableLocalCache: true,
        cacheExpirationTime: 5 * 60 * 1000, // 5 minutes
        maxCacheSize: 50, // 50MB
        enableOptimisticUpdates: true,
        syncOnReconnect: true,
        retryFailedOperations: true
    };
    constructor(config = {}) {
        super();
        this.config = config;
        this.config = { ...this.defaultConfig, ...config };
        this.networkDetector = new network_detector_service_1.NetworkDetector();
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        try {
            console.log('🔧 Initializing Offline-First Data Service...');
            // Ensure local database is ready
            await this.ensureLocalDatabaseReady();
            // Load pending operations from database
            await this.loadPendingOperations();
            // Set up network event listeners
            this.setupNetworkListeners();
            // Start background sync if online
            if (this.networkDetector.isOnline()) {
                this.startBackgroundSync();
            }
            this.isInitialized = true;
            console.log('✅ Offline-First Data Service initialized');
        }
        catch (error) {
            console.error('❌ Failed to initialize Offline-First Data Service:', error);
            throw error;
        }
    }
    /**
     * Primary data access method - always tries local first
     */
    async getData(table, query, params = [], options = {}) {
        const cacheKey = this.generateCacheKey(table, query, params);
        // Check cache first if enabled
        if (options.useCache && !options.forceRefresh && this.config.enableLocalCache) {
            const cached = this.getFromCache(cacheKey);
            if (cached && !cached.isStale) {
                console.log(`📦 Cache hit for ${table}`);
                return { rows: [cached.data], rowCount: 1, command: 'SELECT' };
            }
        }
        try {
            // Always try local database first
            console.log(`🗄️  Fetching from local database: ${table}`);
            const result = await connection_1.dbConnection.executeQuery(query, params);
            // Cache the result if enabled
            if (this.config.enableLocalCache) {
                this.setCache(cacheKey, result.rows, 'local');
            }
            return result;
        }
        catch (error) {
            console.error(`❌ Local database query failed for ${table}:`, error);
            // If local fails, try cache as fallback
            if (this.config.enableLocalCache) {
                const cached = this.getFromCache(cacheKey);
                if (cached) {
                    console.log(`🔄 Using stale cache data for ${table}`);
                    return { rows: [cached.data], rowCount: 1, command: 'SELECT' };
                }
            }
            throw new types_1.DatabaseError(`Failed to fetch data from ${table}`, 'OFFLINE_DATA_ERROR', error);
        }
    }
    /**
     * Offline-first data mutation with optimistic updates
     */
    async mutateData(operation, table, query, params = [], recordId, userId, optimisticData) {
        try {
            // 1. Apply optimistic update immediately if enabled
            if (this.config.enableOptimisticUpdates && optimisticData) {
                this.emit('optimisticUpdate', {
                    operation,
                    table,
                    recordId,
                    data: optimisticData
                });
            }
            // 2. Execute locally first
            console.log(`💾 Executing ${operation} locally on ${table}`);
            const result = await connection_1.dbConnection.executeQuery(query, params);
            // 3. Queue for background sync
            await this.queueOperation(operation, table, recordId, result.rows[0] || params, userId);
            // 4. Trigger sync if online
            if (this.networkDetector.isOnline()) {
                this.triggerBackgroundSync();
            }
            return result;
        }
        catch (error) {
            console.error(`❌ Local mutation failed for ${table}:`, error);
            // Revert optimistic update if it was applied
            if (this.config.enableOptimisticUpdates && optimisticData) {
                this.emit('revertOptimisticUpdate', {
                    operation,
                    table,
                    recordId,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
            throw new types_1.DatabaseError(`Failed to execute ${operation} on ${table}`, 'OFFLINE_MUTATION_ERROR', error);
        }
    }
    /**
     * Check if application can work offline
     */
    isOfflineCapable() {
        return connection_1.dbConnection.getConnectionStatus().isConnected;
    }
    /**
     * Get offline status and capabilities
     */
    getOfflineStatus() {
        return {
            isOfflineCapable: this.isOfflineCapable(),
            isOnline: this.networkDetector.isOnline(),
            pendingOperations: this.operationQueue.length,
            lastSyncAt: sync_manager_service_1.syncManager.getSyncStatus().lastSyncAt,
            localDatabaseStatus: connection_1.dbConnection.getConnectionStatus(),
            cacheSize: this.dataCache.size,
            networkQuality: this.networkDetector.getStatus().connectionQuality
        };
    }
    /**
     * Force sync all pending operations
     */
    async forceSyncPendingOperations() {
        if (!this.networkDetector.isOnline()) {
            throw new Error('Cannot sync while offline');
        }
        console.log(`🔄 Force syncing ${this.operationQueue.length} pending operations`);
        for (const operation of this.operationQueue) {
            try {
                await this.syncOperation(operation);
            }
            catch (error) {
                console.error(`Failed to sync operation ${operation.id}:`, error);
                operation.retryCount++;
                operation.status = 'failed';
            }
        }
        // Remove completed operations
        this.operationQueue = this.operationQueue.filter(op => op.status !== 'completed');
        await this.persistOperationQueue();
    }
    /**
     * Clear local cache
     */
    clearCache() {
        this.dataCache.clear();
        console.log('🧹 Local cache cleared');
    }
    /**
     * Get cache statistics
     */
    getCacheStats() {
        const now = new Date();
        const entries = Array.from(this.dataCache.values());
        return {
            totalEntries: entries.length,
            staleEntries: entries.filter(entry => entry.isStale || entry.expiresAt < now).length,
            localEntries: entries.filter(entry => entry.source === 'local').length,
            remoteEntries: entries.filter(entry => entry.source === 'remote').length,
            estimatedSize: this.estimateCacheSize()
        };
    }
    // Private methods
    async ensureLocalDatabaseReady() {
        if (!connection_1.dbConnection.getConnectionStatus().isConnected) {
            throw new Error('Local database is not connected');
        }
        // Ensure offline operation tracking table exists
        await connection_1.dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS offline_operations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        operation_type VARCHAR(10) NOT NULL,
        table_name VARCHAR(50) NOT NULL,
        record_id VARCHAR(64) NOT NULL,
        data TEXT NOT NULL,
        user_id VARCHAR(64) NOT NULL,
        timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        retry_count INTEGER DEFAULT 0,
        status VARCHAR(20) DEFAULT 'pending',
        priority VARCHAR(10) DEFAULT 'medium'
      )
    `);
    }
    async loadPendingOperations() {
        try {
            const result = await connection_1.dbConnection.executeQuery('SELECT * FROM offline_operations WHERE status IN ($1, $2) ORDER BY timestamp ASC', ['pending', 'failed']);
            this.operationQueue = result.rows.map(row => ({
                ...row,
                data: JSON.parse(row.data),
                timestamp: new Date(row.timestamp)
            }));
            console.log(`📋 Loaded ${this.operationQueue.length} pending operations`);
        }
        catch (error) {
            console.warn('Failed to load pending operations:', error);
            this.operationQueue = [];
        }
    }
    async queueOperation(operation, table, recordId, data, userId, priority = 'medium') {
        const offlineOp = {
            id: crypto.randomUUID(),
            operation,
            table,
            recordId,
            data,
            userId,
            timestamp: new Date(),
            retryCount: 0,
            status: 'pending',
            priority
        };
        this.operationQueue.push(offlineOp);
        await this.persistOperationQueue();
        console.log(`📝 Queued ${operation} operation for ${table}:${recordId}`);
    }
    async persistOperationQueue() {
        // This is a simplified version - in production you'd want to be more efficient
        try {
            await connection_1.dbConnection.executeQuery('DELETE FROM offline_operations WHERE status = $1', ['pending']);
            for (const op of this.operationQueue.filter(op => op.status === 'pending')) {
                await connection_1.dbConnection.executeQuery(`
          INSERT INTO offline_operations 
          (id, operation_type, table_name, record_id, data, user_id, timestamp, retry_count, status, priority)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        `, [
                    op.id, op.operation, op.table, op.recordId, JSON.stringify(op.data),
                    op.userId, op.timestamp, op.retryCount, op.status, op.priority
                ]);
            }
        }
        catch (error) {
            console.error('Failed to persist operation queue:', error);
        }
    }
    setupNetworkListeners() {
        this.networkDetector.on('online', () => {
            console.log('🌐 Network came online - starting background sync');
            if (this.config.syncOnReconnect) {
                this.startBackgroundSync();
            }
        });
        this.networkDetector.on('offline', () => {
            console.log('📴 Network went offline - entering offline mode');
            this.emit('offlineMode', true);
        });
    }
    startBackgroundSync() {
        if (this.operationQueue.length > 0) {
            console.log(`🔄 Starting background sync for ${this.operationQueue.length} operations`);
            this.triggerBackgroundSync();
        }
    }
    async triggerBackgroundSync() {
        // Use the existing sync manager for background sync
        try {
            await sync_manager_service_1.syncManager.performSync();
        }
        catch (error) {
            console.warn('Background sync failed:', error);
        }
    }
    async syncOperation(operation) {
        // This would integrate with the sync manager to push the operation to cloud
        operation.status = 'syncing';
        try {
            await sync_manager_service_1.syncManager.queueOperation(operation.operation, operation.table, operation.recordId, operation.data, operation.userId, operation.priority);
            operation.status = 'completed';
        }
        catch (error) {
            operation.status = 'failed';
            throw error;
        }
    }
    generateCacheKey(table, query, params) {
        return `${table}:${Buffer.from(query + JSON.stringify(params)).toString('base64')}`;
    }
    getFromCache(key) {
        const cached = this.dataCache.get(key);
        if (!cached)
            return null;
        const now = new Date();
        if (cached.expiresAt < now) {
            this.dataCache.delete(key);
            return null;
        }
        return cached;
    }
    setCache(key, data, source) {
        const now = new Date();
        const expiresAt = new Date(now.getTime() + this.config.cacheExpirationTime);
        this.dataCache.set(key, {
            data,
            timestamp: now,
            expiresAt,
            source,
            isStale: false
        });
        // Clean up cache if it gets too large
        if (this.dataCache.size > 1000) { // Arbitrary limit
            this.cleanupCache();
        }
    }
    cleanupCache() {
        const now = new Date();
        const entries = Array.from(this.dataCache.entries());
        // Remove expired entries first
        entries.forEach(([key, value]) => {
            if (value.expiresAt < now) {
                this.dataCache.delete(key);
            }
        });
        // If still too large, remove oldest entries
        if (this.dataCache.size > 1000) {
            const sortedEntries = entries
                .filter(([key]) => this.dataCache.has(key))
                .sort(([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime());
            const toRemove = sortedEntries.slice(0, this.dataCache.size - 800);
            toRemove.forEach(([key]) => this.dataCache.delete(key));
        }
    }
    estimateCacheSize() {
        // Rough estimation in bytes
        let size = 0;
        this.dataCache.forEach(value => {
            size += JSON.stringify(value).length * 2; // Rough estimate
        });
        return size;
    }
    async destroy() {
        this.networkDetector.stopMonitoring();
        this.clearCache();
        this.operationQueue = [];
        this.isInitialized = false;
        console.log('🧹 Offline-First Data Service destroyed');
    }
}
exports.OfflineFirstDataService = OfflineFirstDataService;
// Export singleton instance
exports.offlineFirstDataService = new OfflineFirstDataService();
