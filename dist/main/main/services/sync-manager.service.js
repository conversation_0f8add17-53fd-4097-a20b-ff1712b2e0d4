"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncManager = exports.SyncManager = void 0;
const events_1 = require("events");
const sync_engine_service_1 = require("./sync-engine.service");
const conflict_resolver_service_1 = require("./conflict-resolver.service");
const network_detector_service_1 = require("./network-detector.service");
const service_1 = require("../mcp/service");
const connection_1 = require("../database/connection");
class SyncManager extends events_1.EventEmitter {
    config;
    syncEngine;
    conflictResolver;
    networkDetector;
    syncStatus = {
        status: 'idle',
        lastSyncAt: null,
        pendingChanges: 0
    };
    autoSyncTimer = null;
    isInitialized = false;
    defaultConfig = {
        autoSyncEnabled: true,
        syncInterval: 30000, // 30 seconds
        conflictResolutionStrategy: 'last-write-wins',
        batchSize: 25,
        maxRetries: 3,
        enableRealTimeSync: false
    };
    constructor(config = {}) {
        super();
        this.config = config;
        this.config = { ...this.defaultConfig, ...config };
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        try {
            console.log('Initializing Sync Manager...');
            // Initialize network detector
            this.networkDetector = new network_detector_service_1.NetworkDetector({
                checkInterval: 15000, // Check every 15 seconds
                timeout: 5000,
                maxRetries: 3,
                testUrls: ['https://www.google.com', 'https://api.motherduck.com'],
                latencyThresholds: {
                    excellent: 100,
                    good: 500,
                    poor: 2000
                }
            });
            // Initialize conflict resolver
            this.conflictResolver = new conflict_resolver_service_1.ConflictResolver(this.config.conflictResolutionStrategy);
            // Initialize sync engine
            const syncConfig = {
                syncInterval: this.config.syncInterval,
                batchSize: this.config.batchSize,
                maxRetries: this.config.maxRetries,
                conflictResolutionStrategy: this.config.conflictResolutionStrategy,
                enableRealTime: this.config.enableRealTimeSync,
                compressionEnabled: true,
                deltaSync: true
            };
            this.syncEngine = new sync_engine_service_1.SyncEngine(syncConfig, this.conflictResolver, this.networkDetector);
            // Set up event listeners
            this.setupEventListeners();
            // Create necessary database tables for sync
            await this.ensureSyncTables();
            // Start auto-sync if enabled
            if (this.config.autoSyncEnabled) {
                await this.startAutoSync();
            }
            // Update initial status
            await this.refreshSyncStatus();
            this.isInitialized = true;
            console.log('Sync Manager initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize Sync Manager:', error);
            throw error;
        }
    }
    async ensureSyncTables() {
        try {
            // Create sync_state table for device tracking
            await connection_1.dbConnection.executeQuery(`
        CREATE TABLE IF NOT EXISTS sync_state (
          device_id VARCHAR(32) PRIMARY KEY,
          vector_clock TEXT NOT NULL DEFAULT '{}',
          last_sync_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // Create sync_queue table for operation queuing
            await connection_1.dbConnection.executeQuery(`
        CREATE TABLE IF NOT EXISTS sync_queue (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          device_id VARCHAR(32) NOT NULL,
          operation_id VARCHAR(64) NOT NULL,
          operation_type VARCHAR(20) NOT NULL CHECK (operation_type IN ('CREATE', 'UPDATE', 'DELETE')),
          table_name VARCHAR(50) NOT NULL,
          record_id VARCHAR(64) NOT NULL,
          data TEXT NOT NULL,
          vector_clock TEXT NOT NULL,
          user_id VARCHAR(64) NOT NULL,
          priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
          retry_count INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'syncing', 'synced', 'failed', 'conflict')),
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // Create sync_metadata table for conflict tracking (if it doesn't exist from schema)
            await connection_1.dbConnection.executeQuery(`
        CREATE TABLE IF NOT EXISTS sync_metadata (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          table_name VARCHAR(50) NOT NULL,
          record_id VARCHAR(64) NOT NULL,
          local_version INTEGER DEFAULT 1,
          remote_version INTEGER DEFAULT 0,
          sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'conflict', 'error')),
          last_synced_at TIMESTAMPTZ,
          conflict_data TEXT,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(table_name, record_id)
        )
      `);
            console.log('Sync tables ensured');
        }
        catch (error) {
            console.error('Failed to create sync tables:', error);
            throw error;
        }
    }
    setupEventListeners() {
        // Network events
        this.networkDetector.on('online', () => {
            console.log('Network online - triggering sync');
            this.updateSyncStatus({ status: 'idle' });
            if (this.isInitialized) {
                this.performSync().catch(console.error);
            }
        });
        this.networkDetector.on('offline', () => {
            console.log('Network offline - pausing sync');
            this.updateSyncStatus({ status: 'offline' });
        });
        // Sync engine events
        this.syncEngine.on('sync:started', () => {
            this.updateSyncStatus({ status: 'syncing' });
            this.emit('syncStarted');
        });
        this.syncEngine.on('sync:completed', (result) => {
            this.updateSyncStatus({
                status: result.success ? 'idle' : 'error',
                lastSyncAt: new Date(),
                errorMessage: result.errors.length > 0 ? result.errors.join('; ') : undefined
            });
            this.emit('syncCompleted', result);
        });
        this.syncEngine.on('sync:error', (error) => {
            this.updateSyncStatus({
                status: 'error',
                errorMessage: error.message
            });
            this.emit('syncError', error);
        });
        this.syncEngine.on('sync:conflictDetected', (conflict) => {
            this.emit('conflictDetected', conflict);
        });
        this.syncEngine.on('sync:operationQueued', (operation) => {
            this.updatePendingChangesCount();
            this.emit('operationQueued', operation);
        });
    }
    async startAutoSync() {
        if (this.autoSyncTimer) {
            return; // Already running
        }
        console.log(`Starting auto-sync with interval: ${this.config.syncInterval}ms`);
        this.autoSyncTimer = setInterval(async () => {
            if (this.networkDetector.isOnline() && this.syncStatus.status === 'idle') {
                await this.performSync();
            }
        }, this.config.syncInterval);
        // Perform initial sync after a delay to ensure everything is initialized
        if (this.networkDetector.isOnline()) {
            setTimeout(() => {
                if (this.isInitialized) {
                    this.performSync().catch(console.error);
                }
            }, 2000);
        }
    }
    stopAutoSync() {
        if (this.autoSyncTimer) {
            clearInterval(this.autoSyncTimer);
            this.autoSyncTimer = null;
            console.log('Auto-sync stopped');
        }
    }
    async performSync() {
        if (!this.isInitialized) {
            throw new Error('Sync Manager not initialized');
        }
        if (!this.networkDetector.isOnline()) {
            throw new Error('Cannot sync while offline');
        }
        if (!service_1.mcpService.isConnected()) {
            throw new Error('MCP service not connected');
        }
        try {
            console.log('Starting manual sync...');
            return await this.syncEngine.startSync();
        }
        catch (error) {
            console.error('Manual sync failed:', error);
            throw error;
        }
    }
    async queueOperation(operation, table, recordId, data, userId, priority = 'medium') {
        if (!this.isInitialized) {
            throw new Error('Sync Manager not initialized');
        }
        await this.syncEngine.queueOperation({
            operation,
            table,
            recordId,
            data,
            localTimestamp: new Date(),
            userId,
            dependencies: [],
            priority
        });
        await this.updatePendingChangesCount();
    }
    getSyncStatus() {
        return { ...this.syncStatus };
    }
    async getQueueStatus() {
        if (!this.isInitialized) {
            return {
                totalOperations: 0,
                pendingOperations: 0,
                failedOperations: 0,
                conflictOperations: 0,
                isOnline: false,
                isSyncing: false
            };
        }
        return await this.syncEngine.getQueueStatus();
    }
    isOnline() {
        return this.networkDetector?.isOnline() || false;
    }
    getNetworkStatus() {
        return this.networkDetector?.getStatus();
    }
    updateSyncStatus(updates) {
        this.syncStatus = { ...this.syncStatus, ...updates };
        this.emit('statusChanged', this.syncStatus);
    }
    async updatePendingChangesCount() {
        try {
            const queueStatus = await this.getQueueStatus();
            this.updateSyncStatus({ pendingChanges: queueStatus.pendingOperations });
        }
        catch (error) {
            console.warn('Failed to update pending changes count:', error);
        }
    }
    async refreshSyncStatus() {
        try {
            const queueStatus = await this.getQueueStatus();
            const networkStatus = this.networkDetector.getStatus();
            this.updateSyncStatus({
                status: networkStatus.isOnline ? 'idle' : 'offline',
                pendingChanges: queueStatus.pendingOperations
            });
        }
        catch (error) {
            console.warn('Failed to update sync status:', error);
        }
    }
    // Force push all pending changes
    async forcePushChanges() {
        console.log('Force pushing all pending changes...');
        return await this.performSync();
    }
    // Force pull all remote changes
    async forcePullChanges() {
        console.log('Force pulling all remote changes...');
        // This would be implemented by the sync engine
        return await this.performSync();
    }
    // Clear all pending operations (use with caution)
    async clearQueue() {
        if (!this.isInitialized) {
            return;
        }
        await this.syncEngine.clearQueue();
        await this.updatePendingChangesCount();
        console.log('Sync queue cleared');
    }
    // Get conflicts that require manual resolution
    async getConflicts() {
        try {
            const result = await connection_1.dbConnection.executeQuery('SELECT * FROM sync_metadata WHERE sync_status = $1', ['conflict']);
            return result.rows;
        }
        catch (error) {
            console.error('Failed to get conflicts:', error);
            return [];
        }
    }
    // Resolve a specific conflict
    async resolveConflict(conflictId, resolution, mergedData) {
        try {
            // Implementation would depend on the specific conflict resolution logic
            console.log(`Resolving conflict ${conflictId} with strategy: ${resolution}`);
            // Update sync metadata to mark conflict as resolved
            await connection_1.dbConnection.executeQuery('UPDATE sync_metadata SET sync_status = $1, conflict_data = NULL WHERE id = $2', ['synced', conflictId]);
            this.emit('conflictResolved', { conflictId, resolution });
        }
        catch (error) {
            console.error('Failed to resolve conflict:', error);
            throw error;
        }
    }
    async destroy() {
        console.log('Destroying Sync Manager...');
        this.stopAutoSync();
        if (this.syncEngine) {
            await this.syncEngine.stop();
        }
        if (this.networkDetector) {
            this.networkDetector.destroy();
        }
        this.removeAllListeners();
        this.isInitialized = false;
        console.log('Sync Manager destroyed');
    }
}
exports.SyncManager = SyncManager;
// Export singleton instance
exports.syncManager = new SyncManager();
