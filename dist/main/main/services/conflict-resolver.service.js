"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConflictResolver = void 0;
class ConflictResolver {
    defaultStrategy;
    customResolvers = new Map();
    constructor(defaultStrategy = 'last-write-wins') {
        this.defaultStrategy = defaultStrategy;
        // Register built-in resolvers
        this.registerCustomResolver('todos', ConflictResolver.createTodoResolver());
        this.registerCustomResolver('categories', ConflictResolver.createCategoryResolver());
    }
    async resolve(conflict) {
        // Check for custom resolver for this data type
        const tableName = conflict.operation?.table || conflict.remoteData.table;
        const customResolver = this.customResolvers.get(tableName);
        if (customResolver) {
            return await customResolver(conflict);
        }
        // Use default resolution strategy
        return await this.applyDefaultStrategy(conflict);
    }
    async applyDefaultStrategy(conflict) {
        switch (this.defaultStrategy) {
            case 'last-write-wins':
                return this.lastWriteWinsStrategy(conflict);
            case 'manual':
                return { strategy: 'manual', requiresManualIntervention: true };
            case 'merge':
                return this.mergeStrategy(conflict);
            default:
                return { strategy: 'manual', requiresManualIntervention: true };
        }
    }
    lastWriteWinsStrategy(conflict) {
        if (!conflict.operation) {
            return { strategy: 'remote', resolvedData: conflict.remoteData };
        }
        const localTimestamp = new Date(conflict.operation.localTimestamp);
        const remoteTimestamp = new Date(conflict.remoteData.updated_at);
        if (localTimestamp > remoteTimestamp) {
            return { strategy: 'local', resolvedData: conflict.operation.data };
        }
        else {
            return { strategy: 'remote', resolvedData: conflict.remoteData };
        }
    }
    mergeStrategy(conflict) {
        if (!conflict.operation) {
            return { strategy: 'remote', resolvedData: conflict.remoteData };
        }
        const localData = conflict.operation.data;
        const remoteData = conflict.remoteData;
        // Simple merge strategy - combine non-conflicting fields
        const mergedData = { ...remoteData };
        // For each field in local data, decide how to merge
        for (const [key, localValue] of Object.entries(localData)) {
            const remoteValue = remoteData[key];
            if (remoteValue === undefined || remoteValue === null) {
                // Remote doesn't have this field, use local
                mergedData[key] = localValue;
            }
            else if (localValue !== remoteValue) {
                // Values differ, use merge logic
                mergedData[key] = this.combineValues(localValue, remoteValue);
            }
            // If values are the same, keep remote value (already in mergedData)
        }
        return { strategy: 'merge', resolvedData: mergedData };
    }
    combineValues(localValue, remoteValue) {
        if (Array.isArray(localValue) && Array.isArray(remoteValue)) {
            // Combine arrays and remove duplicates
            return [...new Set([...localValue, ...remoteValue])];
        }
        if (typeof localValue === 'string' && typeof remoteValue === 'string') {
            // For strings, concatenate with separator
            return `${localValue} | ${remoteValue}`;
        }
        // Default to remote value for other types
        return remoteValue;
    }
    registerCustomResolver(tableName, resolver) {
        this.customResolvers.set(tableName, resolver);
    }
    // Todo-specific conflict resolver
    static createTodoResolver() {
        return async (conflict) => {
            if (!conflict.operation) {
                return { strategy: 'remote', resolvedData: conflict.remoteData };
            }
            const localData = conflict.operation.data;
            const remoteData = conflict.remoteData;
            // Special handling for todo fields
            const mergedData = { ...remoteData };
            // Title: Use the longer/more descriptive one
            if (localData.title && remoteData.title) {
                mergedData.title = localData.title.length > remoteData.title.length
                    ? localData.title
                    : remoteData.title;
            }
            // Description: Merge if both exist
            if (localData.description && remoteData.description && localData.description !== remoteData.description) {
                mergedData.description = `${localData.description}\n\n---\n\n${remoteData.description}`;
            }
            else if (localData.description) {
                mergedData.description = localData.description;
            }
            // Status: Use the more progressed status
            if (localData.status && remoteData.status) {
                mergedData.status = ConflictResolver.getMoreProgressedStatus(localData.status, remoteData.status);
            }
            // Priority: Use the higher priority
            if (localData.priority && remoteData.priority) {
                mergedData.priority = ConflictResolver.getHigherPriority(localData.priority, remoteData.priority);
            }
            // Tags: Combine unique tags
            if (localData.tags && remoteData.tags) {
                mergedData.tags = [...new Set([...localData.tags, ...remoteData.tags])];
            }
            else if (localData.tags) {
                mergedData.tags = localData.tags;
            }
            // Due date: Use the earlier date if both exist
            if (localData.due_date && remoteData.due_date) {
                const localDate = new Date(localData.due_date);
                const remoteDate = new Date(remoteData.due_date);
                mergedData.due_date = localDate < remoteDate ? localData.due_date : remoteData.due_date;
            }
            else if (localData.due_date) {
                mergedData.due_date = localData.due_date;
            }
            // Completed: If either is completed, use completed
            if (localData.completed || remoteData.completed) {
                mergedData.completed = true;
                mergedData.completed_at = localData.completed_at || remoteData.completed_at || new Date().toISOString();
            }
            return { strategy: 'merge', resolvedData: mergedData };
        };
    }
    // Category-specific conflict resolver
    static createCategoryResolver() {
        return async (conflict) => {
            if (!conflict.operation) {
                return { strategy: 'remote', resolvedData: conflict.remoteData };
            }
            const localData = conflict.operation.data;
            const remoteData = conflict.remoteData;
            // For categories, name conflicts are critical
            if (localData.name !== remoteData.name) {
                // Require manual resolution for name conflicts
                return { strategy: 'manual', requiresManualIntervention: true };
            }
            // Merge other fields
            const mergedData = { ...remoteData };
            // Color: Use local if it's different (user preference)
            if (localData.color && localData.color !== remoteData.color) {
                mergedData.color = localData.color;
            }
            // Icon: Use local if it's different (user preference)
            if (localData.icon && localData.icon !== remoteData.icon) {
                mergedData.icon = localData.icon;
            }
            return { strategy: 'merge', resolvedData: mergedData };
        };
    }
    static getMoreProgressedStatus(local, remote) {
        const statusOrder = {
            'pending': 0,
            'in_progress': 1,
            'completed': 2,
            'archived': 3,
            'cancelled': 1 // Cancelled is not necessarily more progressed
        };
        const localOrder = statusOrder[local] || 0;
        const remoteOrder = statusOrder[remote] || 0;
        // Special case: if one is cancelled and the other is not, prefer the non-cancelled
        if (local === 'cancelled' && remote !== 'cancelled') {
            return remote;
        }
        if (remote === 'cancelled' && local !== 'cancelled') {
            return local;
        }
        return localOrder >= remoteOrder ? local : remote;
    }
    static getHigherPriority(local, remote) {
        const priorities = {
            'very_low': 0,
            'low': 1,
            'medium': 2,
            'high': 3,
            'very_high': 4
        };
        const localPriority = priorities[local] || 0;
        const remotePriority = priorities[remote] || 0;
        return localPriority >= remotePriority ? local : remote;
    }
    // Vector clock comparison for causality
    static compareVectorClocks(clock1, clock2) {
        let clock1Before = false;
        let clock2Before = false;
        // Get all device IDs from both clocks
        const allDevices = new Set([...Object.keys(clock1), ...Object.keys(clock2)]);
        for (const deviceId of allDevices) {
            const time1 = clock1[deviceId] || 0;
            const time2 = clock2[deviceId] || 0;
            if (time1 < time2) {
                clock1Before = true;
            }
            else if (time1 > time2) {
                clock2Before = true;
            }
        }
        if (clock1Before && !clock2Before) {
            return 'before';
        }
        else if (clock2Before && !clock1Before) {
            return 'after';
        }
        else {
            return 'concurrent';
        }
    }
    // Advanced conflict detection using vector clocks
    detectConflictType(conflict) {
        if (!conflict.operation) {
            return 'no-conflict';
        }
        const operation = conflict.operation;
        const remoteData = conflict.remoteData;
        // Check if remote data indicates deletion
        if (remoteData.is_deleted) {
            return operation.operation === 'UPDATE' ? 'update-delete' : 'no-conflict';
        }
        // Check for create-create conflicts (same ID created on different devices)
        if (operation.operation === 'CREATE' && remoteData.id === operation.recordId) {
            return 'create-create';
        }
        // Check for update-update conflicts using vector clocks
        if (operation.operation === 'UPDATE') {
            const clockComparison = ConflictResolver.compareVectorClocks(operation.vectorClock, remoteData.vector_clock || {});
            if (clockComparison === 'concurrent') {
                return 'update-update';
            }
        }
        return 'no-conflict';
    }
    // Get conflict resolution recommendations
    getResolutionRecommendations(conflict) {
        const conflictType = this.detectConflictType(conflict);
        const recommendations = [];
        switch (conflictType) {
            case 'update-update':
                recommendations.push('Consider merging changes from both versions');
                recommendations.push('Review field-by-field differences');
                recommendations.push('Use last-write-wins if changes are minor');
                break;
            case 'update-delete':
                recommendations.push('Determine if the item should remain deleted');
                recommendations.push('Consider if the update is more recent than the deletion');
                recommendations.push('Check if deletion was intentional');
                break;
            case 'create-create':
                recommendations.push('Merge the two created items if they represent the same entity');
                recommendations.push('Keep both items if they are genuinely different');
                recommendations.push('Check for duplicate detection rules');
                break;
            default:
                recommendations.push('Apply automatic resolution based on timestamps');
                break;
        }
        return recommendations;
    }
}
exports.ConflictResolver = ConflictResolver;
