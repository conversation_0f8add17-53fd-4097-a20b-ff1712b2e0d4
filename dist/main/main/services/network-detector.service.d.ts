import { EventEmitter } from 'events';
export interface NetworkStatus {
    isOnline: boolean;
    connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
    latency: number;
    lastChecked: Date;
    consecutiveFailures: number;
}
export interface NetworkConfiguration {
    checkInterval: number;
    timeout: number;
    maxRetries: number;
    testUrls: string[];
    latencyThresholds: {
        excellent: number;
        good: number;
        poor: number;
    };
}
export declare class NetworkDetector extends EventEmitter {
    private config;
    private status;
    private checkTimer;
    private isChecking;
    private readonly defaultConfig;
    constructor(config?: NetworkConfiguration);
    startMonitoring(): void;
    stopMonitoring(): void;
    isOnline(): boolean;
    getStatus(): NetworkStatus;
    getConnectionQuality(): 'excellent' | 'good' | 'poor' | 'offline';
    getLatency(): number;
    private checkConnectivity;
    private performConnectivityTest;
    private testDNSResolution;
    private testHTTPSConnection;
    private testMultipleEndpoints;
    private testSingleEndpoint;
    private calculateConnectionQuality;
    forceCheck(): Promise<NetworkStatus>;
    waitForOnline(timeoutMs?: number): Promise<boolean>;
    getStatistics(): {
        uptime: number;
        downtime: number;
        averageLatency: number;
        consecutiveFailures: number;
    };
    isStable(): boolean;
    isGoodForSync(): boolean;
    getRetryDelay(): number;
    getRecommendedBatchSize(): number;
    destroy(): void;
}
