"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncManager = exports.SyncManager = exports.NetworkDetector = exports.ConflictResolver = exports.SyncEngine = exports.connectionPool = exports.ConnectionPoolService = void 0;
var connection_pool_service_1 = require("./connection-pool.service");
Object.defineProperty(exports, "ConnectionPoolService", { enumerable: true, get: function () { return connection_pool_service_1.ConnectionPoolService; } });
Object.defineProperty(exports, "connectionPool", { enumerable: true, get: function () { return connection_pool_service_1.connectionPool; } });
// Sync services
var sync_engine_service_1 = require("./sync-engine.service");
Object.defineProperty(exports, "SyncEngine", { enumerable: true, get: function () { return sync_engine_service_1.SyncEngine; } });
var conflict_resolver_service_1 = require("./conflict-resolver.service");
Object.defineProperty(exports, "ConflictResolver", { enumerable: true, get: function () { return conflict_resolver_service_1.ConflictResolver; } });
var network_detector_service_1 = require("./network-detector.service");
Object.defineProperty(exports, "NetworkDetector", { enumerable: true, get: function () { return network_detector_service_1.NetworkDetector; } });
var sync_manager_service_1 = require("./sync-manager.service");
Object.defineProperty(exports, "SyncManager", { enumerable: true, get: function () { return sync_manager_service_1.SyncManager; } });
Object.defineProperty(exports, "syncManager", { enumerable: true, get: function () { return sync_manager_service_1.syncManager; } });
