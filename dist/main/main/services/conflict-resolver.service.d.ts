import { ConflictInfo } from './sync-engine.service';
export interface ConflictResolution {
    strategy: 'local' | 'remote' | 'merge' | 'manual';
    resolvedData?: any;
    requiresManualIntervention?: boolean;
}
export type ConflictResolverFunction = (conflict: ConflictInfo) => Promise<ConflictResolution>;
export declare class ConflictResolver {
    private defaultStrategy;
    private customResolvers;
    constructor(defaultStrategy?: string);
    resolve(conflict: ConflictInfo): Promise<ConflictResolution>;
    private applyDefaultStrategy;
    private lastWriteWinsStrategy;
    private mergeStrategy;
    private combineValues;
    registerCustomResolver(tableName: string, resolver: ConflictResolverFunction): void;
    static createTodoResolver(): ConflictResolverFunction;
    static createCategoryResolver(): ConflictResolverFunction;
    private static getMoreProgressedStatus;
    private static getHigherPriority;
    static compareVectorClocks(clock1: {
        [deviceId: string]: number;
    }, clock2: {
        [deviceId: string]: number;
    }): 'before' | 'after' | 'concurrent';
    detectConflictType(conflict: ConflictInfo): 'update-update' | 'update-delete' | 'create-create' | 'no-conflict';
    getResolutionRecommendations(conflict: ConflictInfo): string[];
}
