"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkDetector = void 0;
const events_1 = require("events");
const dns = __importStar(require("dns"));
const https = __importStar(require("https"));
class NetworkDetector extends events_1.EventEmitter {
    config;
    status = {
        isOnline: false,
        connectionQuality: 'offline',
        latency: 0,
        lastChecked: new Date(),
        consecutiveFailures: 0
    };
    checkTimer = null;
    isChecking = false;
    defaultConfig = {
        checkInterval: 30000, // 30 seconds
        timeout: 5000, // 5 seconds
        maxRetries: 3,
        testUrls: [
            'https://www.google.com',
            'https://www.cloudflare.com',
            'https://www.github.com'
        ],
        latencyThresholds: {
            excellent: 100, // < 100ms
            good: 500, // < 500ms
            poor: 2000 // < 2000ms
        }
    };
    constructor(config = {}) {
        super();
        this.config = config;
        this.config = { ...this.defaultConfig, ...config };
        this.startMonitoring();
    }
    startMonitoring() {
        if (this.checkTimer) {
            return; // Already monitoring
        }
        // Initial check
        this.checkConnectivity();
        // Set up periodic checks
        this.checkTimer = setInterval(() => {
            this.checkConnectivity();
        }, this.config.checkInterval);
        console.log('Network monitoring started');
    }
    stopMonitoring() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
        console.log('Network monitoring stopped');
    }
    isOnline() {
        return this.status.isOnline;
    }
    getStatus() {
        return { ...this.status };
    }
    getConnectionQuality() {
        return this.status.connectionQuality;
    }
    getLatency() {
        return this.status.latency;
    }
    async checkConnectivity() {
        if (this.isChecking) {
            return; // Avoid concurrent checks
        }
        this.isChecking = true;
        try {
            const startTime = Date.now();
            const isConnected = await this.performConnectivityTest();
            const endTime = Date.now();
            const latency = endTime - startTime;
            const wasOnline = this.status.isOnline;
            const previousQuality = this.status.connectionQuality;
            this.status.isOnline = isConnected;
            this.status.latency = latency;
            this.status.lastChecked = new Date();
            if (isConnected) {
                this.status.consecutiveFailures = 0;
                this.status.connectionQuality = this.calculateConnectionQuality(latency);
            }
            else {
                this.status.consecutiveFailures++;
                this.status.connectionQuality = 'offline';
            }
            // Emit events for status changes
            if (!wasOnline && isConnected) {
                console.log('Network connection established');
                this.emit('online', this.status);
            }
            else if (wasOnline && !isConnected) {
                console.log('Network connection lost');
                this.emit('offline', this.status);
            }
            if (previousQuality !== this.status.connectionQuality) {
                console.log(`Network quality changed: ${previousQuality} -> ${this.status.connectionQuality}`);
                this.emit('qualityChanged', this.status.connectionQuality, previousQuality);
            }
            this.emit('statusUpdate', this.status);
        }
        catch (error) {
            console.error('Network connectivity check failed:', error);
            this.status.consecutiveFailures++;
            this.status.isOnline = false;
            this.status.connectionQuality = 'offline';
            this.status.lastChecked = new Date();
        }
        finally {
            this.isChecking = false;
        }
    }
    async performConnectivityTest() {
        // Try multiple methods to determine connectivity
        const tests = [
            this.testDNSResolution(),
            this.testHTTPSConnection(),
            this.testMultipleEndpoints()
        ];
        try {
            // If any test passes, consider online
            const results = await Promise.allSettled(tests);
            return results.some(result => result.status === 'fulfilled' && result.value === true);
        }
        catch (error) {
            return false;
        }
    }
    async testDNSResolution() {
        return new Promise((resolve) => {
            dns.resolve('google.com', (err) => {
                resolve(!err);
            });
        });
    }
    async testHTTPSConnection() {
        return new Promise((resolve) => {
            const req = https.request({
                hostname: 'www.google.com',
                port: 443,
                path: '/',
                method: 'HEAD',
                timeout: this.config.timeout
            }, (res) => {
                resolve(res.statusCode !== undefined && res.statusCode < 400);
            });
            req.on('error', () => resolve(false));
            req.on('timeout', () => {
                req.destroy();
                resolve(false);
            });
            req.end();
        });
    }
    async testMultipleEndpoints() {
        const testPromises = this.config.testUrls.map(url => this.testSingleEndpoint(url));
        try {
            const results = await Promise.allSettled(testPromises);
            // Consider online if at least one endpoint is reachable
            return results.some(result => result.status === 'fulfilled' && result.value === true);
        }
        catch (error) {
            return false;
        }
    }
    async testSingleEndpoint(url) {
        return new Promise((resolve) => {
            const urlObj = new URL(url);
            const req = https.request({
                hostname: urlObj.hostname,
                port: urlObj.port || 443,
                path: urlObj.pathname,
                method: 'HEAD',
                timeout: this.config.timeout
            }, (res) => {
                resolve(res.statusCode !== undefined && res.statusCode < 400);
            });
            req.on('error', () => resolve(false));
            req.on('timeout', () => {
                req.destroy();
                resolve(false);
            });
            req.end();
        });
    }
    calculateConnectionQuality(latency) {
        if (latency < this.config.latencyThresholds.excellent) {
            return 'excellent';
        }
        else if (latency < this.config.latencyThresholds.good) {
            return 'good';
        }
        else {
            return 'poor';
        }
    }
    // Force an immediate connectivity check
    async forceCheck() {
        await this.checkConnectivity();
        return this.getStatus();
    }
    // Wait for network to come online
    async waitForOnline(timeoutMs = 30000) {
        if (this.isOnline()) {
            return true;
        }
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                this.off('online', onlineHandler);
                resolve(false);
            }, timeoutMs);
            const onlineHandler = () => {
                clearTimeout(timeout);
                resolve(true);
            };
            this.once('online', onlineHandler);
        });
    }
    // Get network statistics
    getStatistics() {
        // This would require tracking historical data
        // For now, return basic current stats
        return {
            uptime: this.status.isOnline ? 100 : 0,
            downtime: this.status.isOnline ? 0 : 100,
            averageLatency: this.status.latency,
            consecutiveFailures: this.status.consecutiveFailures
        };
    }
    // Check if connection is stable (no recent failures)
    isStable() {
        return this.status.isOnline && this.status.consecutiveFailures === 0;
    }
    // Check if connection quality is good enough for sync operations
    isGoodForSync() {
        return this.status.isOnline &&
            this.status.connectionQuality !== 'poor' &&
            this.status.consecutiveFailures < 2;
    }
    // Adaptive retry logic based on network quality
    getRetryDelay() {
        const baseDelay = 1000; // 1 second
        switch (this.status.connectionQuality) {
            case 'excellent':
                return baseDelay;
            case 'good':
                return baseDelay * 2;
            case 'poor':
                return baseDelay * 5;
            case 'offline':
                return baseDelay * 10;
            default:
                return baseDelay;
        }
    }
    // Get recommended batch size based on connection quality
    getRecommendedBatchSize() {
        switch (this.status.connectionQuality) {
            case 'excellent':
                return 50;
            case 'good':
                return 25;
            case 'poor':
                return 10;
            case 'offline':
                return 0;
            default:
                return 10;
        }
    }
    destroy() {
        this.stopMonitoring();
        this.removeAllListeners();
    }
}
exports.NetworkDetector = NetworkDetector;
