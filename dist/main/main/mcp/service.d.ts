import { MCPToolOptions, ConnectionStatus, QueryResult } from '../../shared/types';
export declare class MCPService {
    private static instance;
    private clients;
    private transports;
    private connectionStatus;
    private connectionRetryCount;
    private maxRetries;
    private retryDelay;
    private mcpServers;
    private constructor();
    static getInstance(): MCPService;
    initialize(): Promise<void>;
    private loadMCPServersConfig;
    private connectToAllServers;
    private connectToServer;
    private handleConnectionFailure;
    private setupEventHandlers;
    executeTool(options: MCPToolOptions, serverName?: string): Promise<any>;
    listAvailableTools(serverName?: string): Promise<any>;
    listAvailableResources(serverName?: string): Promise<any>;
    getConnectedServers(): string[];
    executeQuery(sql: string, parameters?: any[]): Promise<QueryResult>;
    createTable(schema: any): Promise<any>;
    insertData(table: string, data: any[]): Promise<any>;
    updateData(table: string, updates: any, where: any): Promise<any>;
    deleteData(table: string, where: any): Promise<any>;
    getConnectionStatus(): ConnectionStatus;
    isConnected(): boolean;
    disconnect(): Promise<void>;
    private disconnectFromServer;
    healthCheck(): Promise<boolean>;
    reconnect(): Promise<void>;
}
export declare const mcpService: MCPService;
