"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealMCPIntegrationTester = void 0;
const service_1 = require("./service");
class RealMCPIntegrationTester {
    results = [];
    addResult(name, status, message, details) {
        const result = {
            name,
            status,
            message,
            details,
            timestamp: new Date()
        };
        this.results.push(result);
        const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
        console.log(`${icon} ${name}: ${message}`);
        if (details) {
            console.log(`   Details:`, JSON.stringify(details, null, 2));
        }
    }
    async runAllTests() {
        console.log('🔗 Real MCP Integration Test');
        console.log('============================\n');
        try {
            await this.testMCPServiceInitialization();
            await this.testServerConnections();
            await this.testToolListing();
            await this.testResourceListing();
            await this.testToolExecution();
            await this.testHealthCheck();
            await this.testReconnection();
            this.printSummary();
        }
        catch (error) {
            console.error('❌ Real MCP integration test suite failed:', error);
            this.addResult('Test Suite', 'FAIL', `Critical error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testMCPServiceInitialization() {
        console.log('1. Testing MCP Service Initialization...');
        try {
            await service_1.mcpService.initialize();
            this.addResult('Service Initialization', 'PASS', 'MCP service initialized successfully');
        }
        catch (error) {
            this.addResult('Service Initialization', 'FAIL', `Failed to initialize MCP service: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testServerConnections() {
        console.log('\n2. Testing Server Connections...');
        try {
            const connectedServers = service_1.mcpService.getConnectedServers();
            const connectionStatus = service_1.mcpService.getConnectionStatus();
            if (connectedServers.length > 0) {
                this.addResult('Server Connections', 'PASS', `Connected to ${connectedServers.length} MCP server(s)`, {
                    servers: connectedServers,
                    connectionStatus
                });
            }
            else {
                this.addResult('Server Connections', 'WARN', 'No MCP servers connected', { connectionStatus });
            }
        }
        catch (error) {
            this.addResult('Server Connections', 'FAIL', `Failed to check server connections: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testToolListing() {
        console.log('\n3. Testing Tool Listing...');
        try {
            const tools = await service_1.mcpService.listAvailableTools();
            this.addResult('Tool Listing', 'PASS', 'Successfully listed available tools', { tools });
        }
        catch (error) {
            this.addResult('Tool Listing', 'WARN', `Failed to list tools: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testResourceListing() {
        console.log('\n4. Testing Resource Listing...');
        try {
            const resources = await service_1.mcpService.listAvailableResources();
            this.addResult('Resource Listing', 'PASS', 'Successfully listed available resources', { resources });
        }
        catch (error) {
            this.addResult('Resource Listing', 'WARN', `Failed to list resources: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testToolExecution() {
        console.log('\n5. Testing Tool Execution...');
        try {
            // Try to execute a simple tool if any are available
            const tools = await service_1.mcpService.listAvailableTools();
            if (tools.servers && Object.keys(tools.servers).length > 0) {
                const firstServerName = Object.keys(tools.servers)[0];
                const firstServerTools = tools.servers[firstServerName];
                if (firstServerTools.tools && firstServerTools.tools.length > 0) {
                    const firstTool = firstServerTools.tools[0];
                    try {
                        const result = await service_1.mcpService.executeTool({
                            name: firstTool.name,
                            arguments: {}
                        }, firstServerName);
                        this.addResult('Tool Execution', 'PASS', `Successfully executed tool '${firstTool.name}'`, { tool: firstTool.name, result });
                    }
                    catch (toolError) {
                        this.addResult('Tool Execution', 'WARN', `Tool execution failed (may be expected): ${toolError instanceof Error ? toolError.message : 'Unknown error'}`, { tool: firstTool.name });
                    }
                }
                else {
                    this.addResult('Tool Execution', 'WARN', 'No tools available for execution testing');
                }
            }
            else {
                this.addResult('Tool Execution', 'WARN', 'No servers available for tool execution testing');
            }
        }
        catch (error) {
            this.addResult('Tool Execution', 'WARN', `Failed to test tool execution: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testHealthCheck() {
        console.log('\n6. Testing Health Check...');
        try {
            const isHealthy = await service_1.mcpService.healthCheck();
            this.addResult('Health Check', isHealthy ? 'PASS' : 'WARN', `Health check result: ${isHealthy ? 'Healthy' : 'Unhealthy'}`);
        }
        catch (error) {
            this.addResult('Health Check', 'FAIL', `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async testReconnection() {
        console.log('\n7. Testing Reconnection...');
        try {
            await service_1.mcpService.reconnect();
            const isConnected = service_1.mcpService.isConnected();
            this.addResult('Reconnection', isConnected ? 'PASS' : 'WARN', `Reconnection ${isConnected ? 'successful' : 'failed'}`);
        }
        catch (error) {
            this.addResult('Reconnection', 'WARN', `Reconnection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    printSummary() {
        console.log('\n📊 Test Summary');
        console.log('================');
        const passed = this.results.filter(r => r.status === 'PASS').length;
        const failed = this.results.filter(r => r.status === 'FAIL').length;
        const warned = this.results.filter(r => r.status === 'WARN').length;
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`⚠️  Warnings: ${warned}`);
        console.log(`📝 Total: ${this.results.length}`);
        if (failed === 0) {
            console.log('\n🎉 Real MCP integration is working!');
        }
        else {
            console.log('\n⚠️  Some tests failed. Check the details above.');
        }
    }
}
exports.RealMCPIntegrationTester = RealMCPIntegrationTester;
// Run the tests if this file is executed directly
if (require.main === module) {
    const tester = new RealMCPIntegrationTester();
    tester.runAllTests().catch(console.error);
}
