"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.mcpService = exports.MCPService = void 0;
const index_js_1 = require("@modelcontextprotocol/sdk/client/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/client/stdio.js");
const config_1 = require("../utils/config");
const types_1 = require("../../shared/types");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class MCPService {
    static instance;
    clients = new Map();
    transports = new Map();
    connectionStatus = {
        isConnected: false,
        connectionAttempts: 0,
    };
    connectionRetryCount = 0;
    maxRetries = 3;
    retryDelay = 1000;
    mcpServers = [];
    constructor() { }
    static getInstance() {
        if (!MCPService.instance) {
            MCPService.instance = new MCPService();
        }
        return MCPService.instance;
    }
    async initialize() {
        try {
            const mcpConfig = config_1.config.getMCPConfig();
            this.maxRetries = mcpConfig.retryAttempts;
            this.retryDelay = mcpConfig.retryDelay;
            // Load MCP servers configuration
            await this.loadMCPServersConfig();
            // Connect to all configured MCP servers
            await this.connectToAllServers();
            await this.setupEventHandlers();
            console.log('MCP service initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize MCP service:', error);
            throw new types_1.MCPError('MCP service initialization failed', 'INIT_ERROR', error);
        }
    }
    async loadMCPServersConfig() {
        try {
            const configPath = path.join(process.cwd(), '.roo', 'mcp.json');
            if (fs.existsSync(configPath)) {
                const configData = fs.readFileSync(configPath, 'utf8');
                const config = JSON.parse(configData);
                this.mcpServers = Object.entries(config.mcpServers).map(([name, serverConfig]) => ({
                    name,
                    ...serverConfig
                }));
                console.log(`Loaded ${this.mcpServers.length} MCP server configurations`);
            }
            else {
                console.warn('No MCP servers configuration found at .roo/mcp.json');
            }
        }
        catch (error) {
            console.error('Failed to load MCP servers configuration:', error);
            throw new types_1.MCPError('Failed to load MCP configuration', 'CONFIG_ERROR', error);
        }
    }
    async connectToAllServers() {
        this.connectionStatus.connectionAttempts++;
        const connectionPromises = this.mcpServers.map(server => this.connectToServer(server));
        const results = await Promise.allSettled(connectionPromises);
        const successfulConnections = results.filter(result => result.status === 'fulfilled').length;
        const failedConnections = results.filter(result => result.status === 'rejected');
        if (successfulConnections > 0) {
            this.connectionStatus.isConnected = true;
            this.connectionStatus.lastConnectionAt = new Date();
            this.connectionRetryCount = 0;
            this.connectionStatus.lastError = undefined;
            console.log(`Successfully connected to ${successfulConnections}/${this.mcpServers.length} MCP servers`);
        }
        else {
            this.connectionStatus.isConnected = false;
            const errors = failedConnections.map(result => result.status === 'rejected' ? result.reason : 'Unknown error');
            this.connectionStatus.lastError = `All connections failed: ${errors.join(', ')}`;
            console.error('Failed to connect to any MCP servers:', errors);
            throw new types_1.MCPError('All MCP server connections failed', 'CONNECTION_ERROR');
        }
    }
    async connectToServer(serverConfig) {
        try {
            console.log(`Connecting to MCP server: ${serverConfig.name}`);
            // Create transport for this specific server
            const transport = new stdio_js_1.StdioClientTransport({
                command: serverConfig.command,
                args: serverConfig.args,
                env: {
                    ...Object.fromEntries(Object.entries(process.env).filter(([_, value]) => value !== undefined)),
                    ...serverConfig.env
                }
            });
            // Create client for this server
            const client = new index_js_1.Client({
                name: 'modern-todo-app',
                version: '1.0.0'
            }, {
                capabilities: {
                    tools: {},
                    resources: {}
                }
            });
            // Actually connect to the real MCP server
            await client.connect(transport);
            // Store the client and transport
            this.clients.set(serverConfig.name, client);
            this.transports.set(serverConfig.name, transport);
            console.log(`Successfully connected to MCP server: ${serverConfig.name}`);
        }
        catch (error) {
            console.error(`Failed to connect to MCP server ${serverConfig.name}:`, error);
            throw new types_1.MCPError(`Connection to ${serverConfig.name} failed`, 'CONNECTION_ERROR', error);
        }
    }
    async handleConnectionFailure(error) {
        if (this.connectionRetryCount < this.maxRetries) {
            this.connectionRetryCount++;
            const delay = this.retryDelay * Math.pow(2, this.connectionRetryCount - 1);
            console.log(`Retrying MCP connection in ${delay}ms (attempt ${this.connectionRetryCount}/${this.maxRetries})`);
            setTimeout(async () => {
                await this.connectToAllServers();
            }, delay);
        }
        else {
            throw new types_1.MCPError(`Failed to connect to MCP server after ${this.maxRetries} attempts: ${error.message}`, 'CONNECTION_ERROR', error);
        }
    }
    async setupEventHandlers() {
        // Setup event handlers for connection status changes
        // This would be implemented based on the actual MCP SDK events
    }
    async executeTool(options, serverName) {
        if (!this.connectionStatus.isConnected) {
            throw new types_1.MCPError('MCP service not connected', 'NOT_CONNECTED');
        }
        try {
            // If no server specified, try to find the first available server that has the tool
            let targetClient;
            let targetServerName;
            if (serverName) {
                targetClient = this.clients.get(serverName);
                targetServerName = serverName;
                if (!targetClient) {
                    throw new types_1.MCPError(`MCP server '${serverName}' not found or not connected`, 'SERVER_NOT_FOUND');
                }
            }
            else {
                // Try to find a server that has the requested tool
                for (const [name, client] of this.clients.entries()) {
                    try {
                        const tools = await client.listTools();
                        if (tools.tools.some(tool => tool.name === options.name)) {
                            targetClient = client;
                            targetServerName = name;
                            break;
                        }
                    }
                    catch (error) {
                        console.warn(`Failed to list tools for server ${name}:`, error);
                    }
                }
                if (!targetClient) {
                    // If no server has the tool, use the first available server
                    const firstServer = this.clients.entries().next().value;
                    if (firstServer) {
                        targetClient = firstServer[1];
                        targetServerName = firstServer[0];
                    }
                }
            }
            if (!targetClient || !targetServerName) {
                throw new types_1.MCPError('No MCP servers available', 'NO_SERVERS');
            }
            console.log(`Executing MCP tool '${options.name}' on server '${targetServerName}'`);
            // Execute the tool on the real MCP server
            const result = await targetClient.callTool({
                name: options.name,
                arguments: options.arguments || {}
            });
            console.log(`Tool '${options.name}' executed successfully on server '${targetServerName}'`);
            return result;
        }
        catch (error) {
            console.error(`Failed to execute MCP tool ${options.name}:`, error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new types_1.MCPError(`Tool execution failed: ${errorMessage}`, 'TOOL_ERROR', error);
        }
    }
    // Real MCP server interaction methods
    async listAvailableTools(serverName) {
        if (!this.connectionStatus.isConnected) {
            throw new types_1.MCPError('MCP service not connected', 'NOT_CONNECTED');
        }
        try {
            if (serverName) {
                const client = this.clients.get(serverName);
                if (!client) {
                    throw new types_1.MCPError(`MCP server '${serverName}' not found`, 'SERVER_NOT_FOUND');
                }
                return await client.listTools();
            }
            else {
                // List tools from all connected servers
                const allTools = { servers: {} };
                for (const [name, client] of this.clients.entries()) {
                    try {
                        const tools = await client.listTools();
                        allTools.servers[name] = tools;
                    }
                    catch (error) {
                        console.warn(`Failed to list tools for server ${name}:`, error);
                        allTools.servers[name] = { error: error instanceof Error ? error.message : 'Unknown error' };
                    }
                }
                return allTools;
            }
        }
        catch (error) {
            console.error('Failed to list available tools:', error);
            throw new types_1.MCPError('Failed to list tools', 'LIST_TOOLS_ERROR', error);
        }
    }
    async listAvailableResources(serverName) {
        if (!this.connectionStatus.isConnected) {
            throw new types_1.MCPError('MCP service not connected', 'NOT_CONNECTED');
        }
        try {
            if (serverName) {
                const client = this.clients.get(serverName);
                if (!client) {
                    throw new types_1.MCPError(`MCP server '${serverName}' not found`, 'SERVER_NOT_FOUND');
                }
                return await client.listResources();
            }
            else {
                // List resources from all connected servers
                const allResources = { servers: {} };
                for (const [name, client] of this.clients.entries()) {
                    try {
                        const resources = await client.listResources();
                        allResources.servers[name] = resources;
                    }
                    catch (error) {
                        console.warn(`Failed to list resources for server ${name}:`, error);
                        allResources.servers[name] = { error: error instanceof Error ? error.message : 'Unknown error' };
                    }
                }
                return allResources;
            }
        }
        catch (error) {
            console.error('Failed to list available resources:', error);
            throw new types_1.MCPError('Failed to list resources', 'LIST_RESOURCES_ERROR', error);
        }
    }
    getConnectedServers() {
        return Array.from(this.clients.keys());
    }
    // Convenience methods for common operations
    async executeQuery(sql, parameters = []) {
        return this.executeTool({
            name: 'execute_query',
            arguments: { sql, parameters }
        });
    }
    async createTable(schema) {
        return this.executeTool({
            name: 'create_table',
            arguments: { schema }
        });
    }
    async insertData(table, data) {
        return this.executeTool({
            name: 'insert_data',
            arguments: { table, data }
        });
    }
    async updateData(table, updates, where) {
        return this.executeTool({
            name: 'update_data',
            arguments: { table, updates, where }
        });
    }
    async deleteData(table, where) {
        return this.executeTool({
            name: 'delete_data',
            arguments: { table, where }
        });
    }
    getConnectionStatus() {
        return { ...this.connectionStatus };
    }
    isConnected() {
        return this.connectionStatus.isConnected;
    }
    async disconnect() {
        try {
            // Disconnect from all servers
            const disconnectPromises = [];
            for (const [serverName, client] of this.clients.entries()) {
                disconnectPromises.push(this.disconnectFromServer(serverName, client));
            }
            await Promise.allSettled(disconnectPromises);
            // Clear all connections
            this.clients.clear();
            this.transports.clear();
            this.connectionStatus.isConnected = false;
            console.log('MCP service disconnected successfully from all servers');
        }
        catch (error) {
            console.error('Error disconnecting MCP service:', error);
            throw new types_1.MCPError('Failed to disconnect MCP service', 'DISCONNECT_ERROR', error);
        }
    }
    async disconnectFromServer(serverName, client) {
        try {
            console.log(`Disconnecting from MCP server: ${serverName}`);
            await client.close();
            const transport = this.transports.get(serverName);
            if (transport) {
                await transport.close();
            }
            console.log(`Successfully disconnected from MCP server: ${serverName}`);
        }
        catch (error) {
            console.error(`Error disconnecting from server ${serverName}:`, error);
        }
    }
    async healthCheck() {
        try {
            if (!this.connectionStatus.isConnected) {
                return false;
            }
            // Test connection by listing tools from at least one server
            const servers = this.getConnectedServers();
            if (servers.length === 0) {
                return false;
            }
            try {
                await this.listAvailableTools(servers[0]);
                return true;
            }
            catch (error) {
                console.warn('MCP health check failed:', error);
                return false;
            }
        }
        catch (error) {
            console.warn('MCP health check failed:', error);
            return false;
        }
    }
    async reconnect() {
        await this.disconnect();
        this.connectionRetryCount = 0;
        await this.connectToAllServers();
    }
}
exports.MCPService = MCPService;
// Export singleton instance
exports.mcpService = MCPService.getInstance();
