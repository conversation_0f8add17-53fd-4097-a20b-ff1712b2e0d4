"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const environment_1 = require("./utils/environment");
const connection_1 = require("./database/connection");
const crypto_service_1 = require("./auth/crypto.service");
const service_1 = require("./mcp/service");
const auth_service_1 = require("./auth/auth.service");
const todo_dao_1 = require("./dao/todo.dao");
const category_dao_1 = require("./dao/category.dao");
const api_1 = require("./api");
const types_1 = require("../shared/types");
const sync_manager_service_1 = require("./services/sync-manager.service");
const offline_first_data_service_1 = require("./services/offline-first-data.service");
const electron_log_1 = __importDefault(require("electron-log"));
// Configure logging
electron_log_1.default.transports.file.level = 'info';
electron_log_1.default.transports.console.level = 'debug';
/**
 * Enhanced error handling for IPC operations
 */
function handleIpcError(error, operation) {
    electron_log_1.default.error(`${operation} error:`, error);
    if (error instanceof types_1.TodoValidationError) {
        // Include validation details in the error message for better user feedback
        const errorMessage = error.getUserMessage();
        const detailedMessage = error.validationErrors.length > 0
            ? `${errorMessage} (${error.code})`
            : errorMessage;
        return (0, api_1.createAPIResponse)(false, null, detailedMessage);
    }
    if (error instanceof types_1.TodoDatabaseError) {
        const errorMessage = error.getUserMessage();
        const detailedMessage = `${errorMessage} (${error.code})`;
        return (0, api_1.createAPIResponse)(false, null, detailedMessage);
    }
    if (error instanceof types_1.TodoBusinessError) {
        const detailedMessage = `${error.message} (${error.code})`;
        return (0, api_1.createAPIResponse)(false, null, detailedMessage);
    }
    // Generic error handling
    const message = error instanceof Error ? error.message : 'Unknown error occurred';
    return (0, api_1.createAPIResponse)(false, null, message);
}
class ModernTodoApp {
    mainWindow = null;
    databaseService = null;
    securityService = null;
    mcpService = null;
    constructor() {
        this.setupApp();
        this.setupServices();
        this.setupIpcHandlers();
    }
    setupApp() {
        // Set app user model id for Windows
        electron_1.app.setAppUserModelId('com.moderntodo.app');
        // Handle app events
        electron_1.app.whenReady().then(() => {
            this.createMainWindow();
            this.setupMenu();
            electron_1.app.on('activate', () => {
                if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        electron_1.app.on('before-quit', async () => {
            await this.cleanup();
        });
    }
    async setupServices() {
        try {
            // Initialize core services
            this.securityService = new crypto_service_1.CryptographyService();
            this.databaseService = connection_1.DatabaseConnection.getInstance();
            this.mcpService = service_1.MCPService.getInstance();
            // Initialize services in order
            await this.databaseService.initialize();
            electron_log_1.default.info('Database service initialized');
            await this.mcpService.initialize();
            electron_log_1.default.info('MCP service initialized');
            // Initialize sync manager (only if MCP is connected)
            if (this.mcpService.isConnected()) {
                await sync_manager_service_1.syncManager.initialize();
                electron_log_1.default.info('Sync manager initialized');
            }
            // Initialize offline-first data service
            await offline_first_data_service_1.offlineFirstDataService.initialize();
            electron_log_1.default.info('Offline-first data service initialized');
        }
        catch (error) {
            electron_log_1.default.error('Failed to initialize services:', error);
            electron_1.app.quit();
        }
    }
    createMainWindow() {
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            show: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: (0, path_1.join)(__dirname, '../preload/index.js'),
                webSecurity: true,
                allowRunningInsecureContent: false,
            },
            titleBarStyle: 'hiddenInset',
            vibrancy: 'under-window', // macOS glassmorphism effect
            backgroundMaterial: 'acrylic', // Windows 11 glassmorphism
            transparent: true,
            frame: false,
        });
        // Load the renderer
        if ((0, environment_1.isDev)()) {
            this.mainWindow.loadURL('http://localhost:5173');
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile((0, path_1.join)(__dirname, '../renderer/index.html'));
        }
        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow?.show();
            if ((0, environment_1.isDev)()) {
                this.mainWindow?.webContents.openDevTools();
            }
        });
        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }
    setupMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'New Todo',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            this.mainWindow?.webContents.send('menu-new-todo');
                        },
                    },
                    { type: 'separator' },
                    {
                        label: 'Import',
                        accelerator: 'CmdOrCtrl+I',
                        click: () => {
                            this.mainWindow?.webContents.send('menu-import');
                        },
                    },
                    {
                        label: 'Export',
                        accelerator: 'CmdOrCtrl+E',
                        click: () => {
                            this.mainWindow?.webContents.send('menu-export');
                        },
                    },
                    { type: 'separator' },
                    {
                        label: 'Quit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            electron_1.app.quit();
                        },
                    },
                ],
            },
            {
                label: 'Edit',
                submenu: [
                    { role: 'undo' },
                    { role: 'redo' },
                    { type: 'separator' },
                    { role: 'cut' },
                    { role: 'copy' },
                    { role: 'paste' },
                    { role: 'selectAll' },
                ],
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' },
                ],
            },
            {
                label: 'Window',
                submenu: [
                    { role: 'minimize' },
                    { role: 'close' },
                ],
            },
        ];
        const menu = electron_1.Menu.buildFromTemplate(template);
        electron_1.Menu.setApplicationMenu(menu);
    }
    setupIpcHandlers() {
        // Database operations
        electron_1.ipcMain.handle('db:query', async (event, sql, params) => {
            try {
                return await this.databaseService?.executeQuery(sql, params);
            }
            catch (error) {
                electron_log_1.default.error('Database query error:', error);
                throw error;
            }
        });
        electron_1.ipcMain.handle('db:transaction', async (event, operations) => {
            try {
                return await this.databaseService?.executeTransaction(async (execute) => {
                    const results = [];
                    for (const op of operations) {
                        results.push(await execute(op.query, op.params));
                    }
                    return results;
                });
            }
            catch (error) {
                electron_log_1.default.error('Database transaction error:', error);
                throw error;
            }
        });
        // Security operations
        electron_1.ipcMain.handle('security:encrypt', async (event, data, password) => {
            try {
                return await this.securityService?.encryptData(data, password);
            }
            catch (error) {
                electron_log_1.default.error('Encryption error:', error);
                throw error;
            }
        });
        electron_1.ipcMain.handle('security:decrypt', async (event, encryptedData, password) => {
            try {
                return await this.securityService?.decryptData(encryptedData, password);
            }
            catch (error) {
                electron_log_1.default.error('Decryption error:', error);
                throw error;
            }
        });
        // MCP operations
        electron_1.ipcMain.handle('mcp:sync', async (event, data) => {
            try {
                return await this.mcpService?.executeQuery(data.sql, data.params);
            }
            catch (error) {
                electron_log_1.default.error('MCP sync error:', error);
                throw error;
            }
        });
        // System operations
        electron_1.ipcMain.handle('system:getInfo', async () => {
            return {
                platform: process.platform,
                arch: process.arch,
                version: electron_1.app.getVersion(),
                electronVersion: process.versions.electron,
                nodeVersion: process.versions.node,
            };
        });
        electron_1.ipcMain.handle('app:quit', () => {
            electron_1.app.quit();
        });
        electron_1.ipcMain.handle('app:minimize', () => {
            this.mainWindow?.minimize();
        });
        electron_1.ipcMain.handle('app:maximize', () => {
            if (this.mainWindow?.isMaximized()) {
                this.mainWindow.unmaximize();
            }
            else {
                this.mainWindow?.maximize();
            }
        });
        electron_1.ipcMain.handle('app:close', () => {
            this.mainWindow?.close();
        });
        // Offline-first todo operations
        electron_1.ipcMain.handle('todos:getAllOfflineFirst', async (event, sessionId, filters, pagination, options) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const result = await todo_dao_1.todoDAO.findByUserIdOfflineFirst(session.userId, pagination, filters, options);
                return (0, api_1.createAPIResponse)(true, result);
            }
            catch (error) {
                return handleIpcError(error, 'Get todos (offline-first)');
            }
        });
        electron_1.ipcMain.handle('todos:createOfflineFirst', async (event, sessionId, todoData, options) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const todo = await todo_dao_1.todoDAO.createTodoOfflineFirst(todoData, session.userId, options);
                return (0, api_1.createAPIResponse)(true, todo);
            }
            catch (error) {
                return handleIpcError(error, 'Create todo (offline-first)');
            }
        });
        electron_1.ipcMain.handle('todos:updateOfflineFirst', async (event, sessionId, todoId, updates, options) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const todo = await todo_dao_1.todoDAO.updateOfflineFirst(todoId, updates, session.userId, options);
                return (0, api_1.createAPIResponse)(true, todo);
            }
            catch (error) {
                return handleIpcError(error, 'Update todo (offline-first)');
            }
        });
        electron_1.ipcMain.handle('todos:updateStatusOfflineFirst', async (event, sessionId, todoId, status, options) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const todo = await todo_dao_1.todoDAO.updateStatusOfflineFirst(todoId, status, session.userId, options);
                return (0, api_1.createAPIResponse)(true, todo);
            }
            catch (error) {
                return handleIpcError(error, 'Update todo status (offline-first)');
            }
        });
        electron_1.ipcMain.handle('todos:deleteOfflineFirst', async (event, sessionId, todoId, options) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const todo = await todo_dao_1.todoDAO.softDeleteTodoOfflineFirst(todoId, session.userId, options);
                return (0, api_1.createAPIResponse)(true, todo);
            }
            catch (error) {
                return handleIpcError(error, 'Delete todo (offline-first)');
            }
        });
        electron_1.ipcMain.handle('todos:getOfflineStatus', async () => {
            try {
                const status = offline_first_data_service_1.offlineFirstDataService.getOfflineStatus();
                return (0, api_1.createAPIResponse)(true, status);
            }
            catch (error) {
                return handleIpcError(error, 'Get offline status');
            }
        });
        electron_1.ipcMain.handle('todos:forceSyncPending', async () => {
            try {
                await offline_first_data_service_1.offlineFirstDataService.forceSyncPendingOperations();
                return (0, api_1.createAPIResponse)(true, { success: true });
            }
            catch (error) {
                return handleIpcError(error, 'Force sync pending operations');
            }
        });
        electron_1.ipcMain.handle('todos:clearCache', async () => {
            try {
                offline_first_data_service_1.offlineFirstDataService.clearCache();
                return (0, api_1.createAPIResponse)(true, { success: true });
            }
            catch (error) {
                return handleIpcError(error, 'Clear cache');
            }
        });
        // Todo operations
        electron_1.ipcMain.handle('todos:getAll', async (event, sessionId, filters, pagination) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const result = await todo_dao_1.todoDAO.findByUserId(session.userId, pagination, filters);
                return (0, api_1.createAPIResponse)(true, result);
            }
            catch (error) {
                electron_log_1.default.error('Get todos error:', error);
                return (0, api_1.createAPIResponse)(false, null, error instanceof Error ? error.message : 'Unknown error');
            }
        });
        electron_1.ipcMain.handle('todos:create', async (event, sessionId, todoData) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const todo = await todo_dao_1.todoDAO.createTodo(todoData, session.userId);
                return (0, api_1.createAPIResponse)(true, todo);
            }
            catch (error) {
                return handleIpcError(error, 'Create todo');
            }
        });
        electron_1.ipcMain.handle('todos:update', async (event, sessionId, todoId, updates) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const todo = await todo_dao_1.todoDAO.updateTodo(todoId, updates, session.userId);
                return (0, api_1.createAPIResponse)(true, todo);
            }
            catch (error) {
                return handleIpcError(error, 'Update todo');
            }
        });
        electron_1.ipcMain.handle('todos:delete', async (event, sessionId, todoId) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const todo = await todo_dao_1.todoDAO.softDeleteTodo(todoId, session.userId);
                return (0, api_1.createAPIResponse)(true, todo);
            }
            catch (error) {
                return handleIpcError(error, 'Delete todo');
            }
        });
        electron_1.ipcMain.handle('todos:updateStatus', async (event, sessionId, todoId, status) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const todo = await todo_dao_1.todoDAO.updateStatus(todoId, status, session.userId);
                return (0, api_1.createAPIResponse)(true, todo);
            }
            catch (error) {
                return handleIpcError(error, 'Update todo status');
            }
        });
        // Category operations
        electron_1.ipcMain.handle('categories:getAll', async (event, sessionId) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const categories = await category_dao_1.categoryDAO.findByUserId(session.userId);
                return (0, api_1.createAPIResponse)(true, categories);
            }
            catch (error) {
                return handleIpcError(error, 'Get categories');
            }
        });
        electron_1.ipcMain.handle('categories:create', async (event, sessionId, categoryData) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const category = await category_dao_1.categoryDAO.createCategory(session.userId, categoryData.name, categoryData.color, categoryData.icon, categoryData.is_default);
                return (0, api_1.createAPIResponse)(true, category);
            }
            catch (error) {
                return handleIpcError(error, 'Create category');
            }
        });
        electron_1.ipcMain.handle('categories:update', async (event, sessionId, categoryId, updates) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const category = await category_dao_1.categoryDAO.updateCategory(categoryId, session.userId, updates);
                return (0, api_1.createAPIResponse)(true, category);
            }
            catch (error) {
                return handleIpcError(error, 'Update category');
            }
        });
        electron_1.ipcMain.handle('categories:delete', async (event, sessionId, categoryId) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const success = await category_dao_1.categoryDAO.deleteCategory(categoryId, session.userId);
                return (0, api_1.createAPIResponse)(true, { success });
            }
            catch (error) {
                return handleIpcError(error, 'Delete category');
            }
        });
        electron_1.ipcMain.handle('categories:reorder', async (event, sessionId, categoryOrders) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const categories = await category_dao_1.categoryDAO.reorderCategories(session.userId, categoryOrders);
                return (0, api_1.createAPIResponse)(true, categories);
            }
            catch (error) {
                return handleIpcError(error, 'Reorder categories');
            }
        });
        // Tag operations
        electron_1.ipcMain.handle('tags:getAll', async (event, sessionId) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const tags = await todo_dao_1.todoDAO.getAllTags(session.userId);
                return (0, api_1.createAPIResponse)(true, tags);
            }
            catch (error) {
                return handleIpcError(error, 'Get all tags');
            }
        });
        electron_1.ipcMain.handle('tags:getSuggestions', async (event, sessionId, query, limit) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const suggestions = await todo_dao_1.todoDAO.getTagSuggestions(session.userId, query, limit);
                return (0, api_1.createAPIResponse)(true, suggestions);
            }
            catch (error) {
                return handleIpcError(error, 'Get tag suggestions');
            }
        });
        electron_1.ipcMain.handle('tags:getPopular', async (event, sessionId, limit) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const popularTags = await todo_dao_1.todoDAO.getPopularTags(session.userId, limit);
                return (0, api_1.createAPIResponse)(true, popularTags);
            }
            catch (error) {
                return handleIpcError(error, 'Get popular tags');
            }
        });
        electron_1.ipcMain.handle('tags:getRecent', async (event, sessionId, limit) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const recentTags = await todo_dao_1.todoDAO.getRecentTags(session.userId, limit);
                return (0, api_1.createAPIResponse)(true, recentTags);
            }
            catch (error) {
                return handleIpcError(error, 'Get recent tags');
            }
        });
        electron_1.ipcMain.handle('tags:getStats', async (event, sessionId, tag) => {
            try {
                const session = await auth_service_1.authService.validateSession(sessionId);
                if (!session) {
                    throw new Error('Invalid session');
                }
                const stats = await todo_dao_1.todoDAO.getTagStats(session.userId, tag);
                return (0, api_1.createAPIResponse)(true, stats);
            }
            catch (error) {
                return handleIpcError(error, 'Get tag stats');
            }
        });
    }
    async cleanup() {
        try {
            // Cleanup services in reverse order
            await offline_first_data_service_1.offlineFirstDataService.destroy();
            await sync_manager_service_1.syncManager.destroy();
            await this.databaseService?.close();
            await this.mcpService?.disconnect();
            electron_log_1.default.info('Application cleanup completed');
        }
        catch (error) {
            electron_log_1.default.error('Cleanup error:', error);
        }
    }
}
// Create and initialize the application
new ModernTodoApp();
