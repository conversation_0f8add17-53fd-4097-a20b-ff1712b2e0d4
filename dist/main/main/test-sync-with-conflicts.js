"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testSyncWithConflicts = testSyncWithConflicts;
const sync_manager_service_1 = require("./services/sync-manager.service");
const connection_1 = require("./database/connection");
const service_1 = require("./mcp/service");
async function testSyncWithConflicts() {
    console.log('🧪 Testing Sync with Conflict Resolution...\n');
    try {
        // Initialize services
        console.log('1. Initializing services...');
        await connection_1.dbConnection.initialize();
        try {
            await service_1.mcpService.initialize();
            console.log('✅ MCP service initialized');
        }
        catch (error) {
            console.log('⚠️  MCP service not available (expected for local testing)');
        }
        await sync_manager_service_1.syncManager.initialize();
        console.log('✅ Sync manager initialized\n');
        // Test 1: Create conflicting operations
        console.log('2. Creating conflicting operations...');
        const testTodo1 = {
            id: 'conflict-todo-123',
            title: 'Test Todo - Local Version',
            description: 'This is the local version of the todo',
            status: 'in_progress',
            priority: 'high',
            tags: ['local', 'test'],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        const testTodo2 = {
            id: 'conflict-todo-123',
            title: 'Test Todo - Remote Version',
            description: 'This is the remote version of the todo',
            status: 'completed',
            priority: 'medium',
            tags: ['remote', 'test', 'completed'],
            created_at: new Date().toISOString(),
            updated_at: new Date(Date.now() + 1000).toISOString() // 1 second later
        };
        // Queue local operation
        await sync_manager_service_1.syncManager.queueOperation('CREATE', 'todos', testTodo1.id, testTodo1, 'test-user-123', 'high');
        console.log('✅ Local operation queued');
        // Simulate remote conflict by manually inserting conflict data
        await connection_1.dbConnection.executeQuery(`INSERT INTO sync_metadata (table_name, record_id, sync_status, conflict_data, created_at, updated_at)
       VALUES ($1, $2, 'conflict', $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       ON CONFLICT (table_name, record_id)
       DO UPDATE SET sync_status = 'conflict', conflict_data = $3, updated_at = CURRENT_TIMESTAMP`, [
            'todos',
            testTodo1.id,
            JSON.stringify({
                operation: null,
                remoteData: testTodo2,
                localData: testTodo1,
                reason: 'Simulated conflict for testing',
                conflictType: 'create-create',
                recommendations: [
                    'Review both versions and merge manually',
                    'Consider which version has more recent changes',
                    'Check if both versions represent the same intent'
                ],
                tableName: 'todos',
                recordId: testTodo1.id
            })
        ]);
        console.log('✅ Conflict data inserted\n');
        // Test 2: Check sync status
        console.log('3. Checking sync status...');
        const syncStatus = sync_manager_service_1.syncManager.getSyncStatus();
        console.log('📊 Sync Status:', JSON.stringify(syncStatus, null, 2));
        const queueStatus = await sync_manager_service_1.syncManager.getQueueStatus();
        console.log('📊 Queue Status:', JSON.stringify(queueStatus, null, 2));
        // Test 3: Get conflicts
        console.log('\n4. Getting conflicts...');
        const conflicts = await sync_manager_service_1.syncManager.getConflicts();
        console.log('⚠️  Found conflicts:', conflicts.length);
        if (conflicts.length > 0) {
            console.log('📋 Conflict details:', JSON.stringify(conflicts[0], null, 2));
        }
        // Test 4: Resolve conflict
        if (conflicts.length > 0) {
            console.log('\n5. Resolving conflict...');
            const conflict = conflicts[0];
            try {
                // Test different resolution strategies
                console.log('   Testing local resolution...');
                // For testing, we'll create a simple resolution
                const mergedData = {
                    ...testTodo1,
                    title: 'Test Todo - Merged Version',
                    description: 'This is a merged version combining both local and remote changes',
                    status: 'completed', // Take from remote
                    priority: 'high', // Take from local
                    tags: ['local', 'remote', 'test', 'merged'], // Combine both
                    updated_at: new Date().toISOString()
                };
                await sync_manager_service_1.syncManager.resolveConflict(conflict.id || 'test-conflict', 'merge', mergedData);
                console.log('✅ Conflict resolved with merge strategy');
            }
            catch (error) {
                console.log('⚠️  Conflict resolution failed (expected without full MCP setup):', error instanceof Error ? error.message : String(error));
            }
        }
        // Test 5: Check status after resolution
        console.log('\n6. Checking status after resolution...');
        const finalSyncStatus = sync_manager_service_1.syncManager.getSyncStatus();
        console.log('📊 Final Sync Status:', JSON.stringify(finalSyncStatus, null, 2));
        const finalQueueStatus = await sync_manager_service_1.syncManager.getQueueStatus();
        console.log('📊 Final Queue Status:', JSON.stringify(finalQueueStatus, null, 2));
        const remainingConflicts = await sync_manager_service_1.syncManager.getConflicts();
        console.log('⚠️  Remaining conflicts:', remainingConflicts.length);
        // Test 6: Test network status
        console.log('\n7. Testing network status...');
        const networkStatus = sync_manager_service_1.syncManager.getNetworkStatus();
        console.log('🌐 Network Status:', JSON.stringify(networkStatus, null, 2));
        // Test 7: Test sync attempt (will fail gracefully without MCP)
        console.log('\n8. Testing sync attempt...');
        try {
            const syncResult = await sync_manager_service_1.syncManager.performSync();
            console.log('✅ Sync completed:', JSON.stringify(syncResult, null, 2));
        }
        catch (error) {
            console.log('⚠️  Sync failed (expected without MCP connection):', error instanceof Error ? error.message : String(error));
        }
        console.log('\n✅ All sync with conflict resolution tests completed!');
    }
    catch (error) {
        console.error('❌ Test failed:', error);
    }
    finally {
        // Cleanup
        try {
            // Clean up test data
            await connection_1.dbConnection.executeQuery('DELETE FROM sync_metadata WHERE record_id = $1', ['conflict-todo-123']);
            await connection_1.dbConnection.executeQuery('DELETE FROM sync_queue WHERE record_id = $1', ['conflict-todo-123']);
            await sync_manager_service_1.syncManager.destroy();
            await connection_1.dbConnection.close();
            console.log('\n🧹 Cleanup completed');
        }
        catch (cleanupError) {
            console.warn('⚠️  Cleanup warning:', cleanupError);
        }
    }
}
// Run the test
if (require.main === module) {
    testSyncWithConflicts()
        .then(() => {
        console.log('\n🎉 Sync with conflict resolution test completed!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('\n💥 Test failed with error:', error);
        process.exit(1);
    });
}
