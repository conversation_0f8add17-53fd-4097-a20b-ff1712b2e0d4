export interface ElectronAPI {
    database: {
        query: (sql: string, params?: any[]) => Promise<any>;
        transaction: (operations: any[]) => Promise<any>;
    };
    security: {
        encrypt: (data: string, password: string) => Promise<any>;
        decrypt: (encryptedData: any, password: string) => Promise<string>;
    };
    mcp: {
        sync: (data: any) => Promise<any>;
    };
    sync: {
        getStatus: () => Promise<any>;
        getQueueStatus: () => Promise<any>;
        getNetworkStatus: () => Promise<any>;
        getConflicts: () => Promise<any>;
        performSync: () => Promise<any>;
        forcePush: () => Promise<any>;
        forcePull: () => Promise<any>;
        resolveConflict: (conflictId: string, resolution: string, mergedData?: any) => Promise<any>;
        clearQueue: () => Promise<any>;
    };
    system: {
        getInfo: () => Promise<SystemInfo>;
        quit: () => Promise<void>;
        minimize: () => Promise<void>;
        maximize: () => Promise<void>;
        close: () => Promise<void>;
    };
    on: (channel: string, callback: (event: any, ...args: any[]) => void) => void;
    removeAllListeners: (channel: string) => void;
}
export interface SystemInfo {
    platform: string;
    arch: string;
    version: string;
    electronVersion: string;
    nodeVersion: string;
}
