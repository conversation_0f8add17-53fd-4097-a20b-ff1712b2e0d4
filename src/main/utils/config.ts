import * as dotenv from 'dotenv';
import { DatabaseConfig, MCPConfig } from '@shared/types';

// Load environment variables
dotenv.config();

export class ConfigService {
  private static instance: ConfigService;

  private constructor() {}

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  public getDatabaseConfig(): DatabaseConfig {
    return {
      path: process.env.DATABASE_PATH || './data/todo.db',
      motherduckToken: process.env.MOTHERDUCK_TOKEN,
      connectionTimeout: parseInt(process.env.CONNECTION_TIMEOUT || '10000'),
      maxConnections: parseInt(process.env.MAX_CONNECTIONS || '5'),
      minConnections: parseInt(process.env.MIN_CONNECTIONS || '1'),
      enableWAL: process.env.ENABLE_WAL === 'true',
      enableEncryption: process.env.ENCRYPTION_ENABLED === 'true',
    };
  }

  public getMCPConfig(): MCPConfig {
    // MotherDuck token is now optional since we support multiple MCP servers
    const motherduckToken = process.env.MOTHERDUCK_TOKEN || '';

    return {
      motherduckToken,
      databaseName: process.env.DATABASE_NAME || 'todo_app',
      connectionTimeout: parseInt(process.env.CONNECTION_TIMEOUT || '10000'),
      retryAttempts: parseInt(process.env.MCP_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.MCP_RETRY_DELAY || '1000'),
      enableMetrics: process.env.ENABLE_MCP_METRICS === 'true',
      logLevel: (process.env.LOG_LEVEL as any) || 'info',
    };
  }

  public getAppConfig() {
    return {
      nodeEnv: process.env.NODE_ENV || 'development',
      logLevel: process.env.LOG_LEVEL || 'debug',
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'),
      maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
      lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000'),
      syncInterval: parseInt(process.env.SYNC_INTERVAL || '30000'),
      enableOfflineMode: process.env.ENABLE_OFFLINE_MODE === 'true',
      autoSync: process.env.AUTO_SYNC === 'true',
      enableDevtools: process.env.ENABLE_DEVTOOLS === 'true',
    };
  }

  public getSecurityConfig() {
    return {
      encryptionEnabled: process.env.ENCRYPTION_ENABLED === 'true',
      require2FA: process.env.REQUIRE_2FA === 'true',
      enableBiometric: process.env.ENABLE_BIOMETRIC === 'true',
      passwordMinLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '12'),
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'),
      maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
      lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000'),
    };
  }

  public validateConfig(): void {
    const requiredEnvVars = ['DATABASE_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }

    // Warn about missing MotherDuck token for cloud features
    if (!process.env.MOTHERDUCK_TOKEN) {
      console.warn('Warning: MOTHERDUCK_TOKEN not set. Cloud sync features will be disabled.');
    }
  }

  public isDevelopment(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  public isProduction(): boolean {
    return process.env.NODE_ENV === 'production';
  }
}

// Export singleton instance
export const config = ConfigService.getInstance();